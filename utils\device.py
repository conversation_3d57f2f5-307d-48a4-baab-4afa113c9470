"""
Device Management Utilities

This module provides utilities for device detection, management, and optimization.
"""

import torch
import logging
from typing import Dict, Any, List, Optional
import platform
import psutil

logger = logging.getLogger(__name__)


def get_device_info() -> Dict[str, Any]:
    """
    Get comprehensive device information.
    
    Returns:
        Device information dictionary
    """
    info = {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "pytorch_version": torch.__version__,
        "cpu": {
            "model": platform.processor(),
            "cores": psutil.cpu_count(logical=False),
            "threads": psutil.cpu_count(logical=True),
            "frequency": psutil.cpu_freq().current if psutil.cpu_freq() else None
        },
        "memory": {
            "total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
            "available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
            "used_percent": psutil.virtual_memory().percent
        },
        "cuda": {
            "available": torch.cuda.is_available(),
            "version": torch.version.cuda if torch.cuda.is_available() else None,
            "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
            "devices": []
        }
    }
    
    # Get CUDA device information
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            device_props = torch.cuda.get_device_properties(i)
            device_info = {
                "id": i,
                "name": device_props.name,
                "total_memory_gb": round(device_props.total_memory / (1024**3), 2),
                "major": device_props.major,
                "minor": device_props.minor,
                "multi_processor_count": device_props.multi_processor_count
            }
            
            # Get current memory usage
            if torch.cuda.is_available():
                torch.cuda.set_device(i)
                memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
                memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
                device_info.update({
                    "memory_allocated_gb": round(memory_allocated, 2),
                    "memory_reserved_gb": round(memory_reserved, 2),
                    "memory_free_gb": round(device_info["total_memory_gb"] - memory_reserved, 2)
                })
            
            info["cuda"]["devices"].append(device_info)
    
    # Check for MPS (Apple Silicon)
    info["mps"] = {
        "available": torch.backends.mps.is_available() if hasattr(torch.backends, 'mps') else False
    }
    
    return info


def get_optimal_device(prefer_gpu: bool = True, gpu_memory_threshold: float = 2.0) -> str:
    """
    Get the optimal device for training/inference.
    
    Args:
        prefer_gpu: Whether to prefer GPU over CPU
        gpu_memory_threshold: Minimum GPU memory in GB required
        
    Returns:
        Device string (e.g., 'cuda:0', 'mps', 'cpu')
    """
    device_info = get_device_info()
    
    if prefer_gpu:
        # Check CUDA availability
        if device_info["cuda"]["available"]:
            # Find GPU with most free memory
            best_gpu = None
            max_free_memory = 0
            
            for gpu in device_info["cuda"]["devices"]:
                free_memory = gpu.get("memory_free_gb", 0)
                if free_memory >= gpu_memory_threshold and free_memory > max_free_memory:
                    max_free_memory = free_memory
                    best_gpu = gpu["id"]
            
            if best_gpu is not None:
                logger.info(f"Selected CUDA device {best_gpu} with {max_free_memory:.1f}GB free memory")
                return f"cuda:{best_gpu}"
        
        # Check MPS availability (Apple Silicon)
        if device_info["mps"]["available"]:
            logger.info("Selected MPS device (Apple Silicon)")
            return "mps"
    
    # Fallback to CPU
    logger.info("Selected CPU device")
    return "cpu"


def optimize_device_settings(device: str, batch_size: Optional[int] = None) -> Dict[str, Any]:
    """
    Get optimized settings for the specified device.
    
    Args:
        device: Device string
        batch_size: Optional batch size for optimization
        
    Returns:
        Optimization settings
    """
    settings = {
        "device": device,
        "num_workers": 4,  # Default
        "pin_memory": False,
        "persistent_workers": False
    }
    
    if device.startswith("cuda"):
        # CUDA optimizations
        gpu_id = int(device.split(":")[-1]) if ":" in device else 0
        device_info = get_device_info()
        
        if gpu_id < len(device_info["cuda"]["devices"]):
            gpu_info = device_info["cuda"]["devices"][gpu_id]
            gpu_memory = gpu_info.get("total_memory_gb", 0)
            
            # Optimize based on GPU memory
            if gpu_memory >= 24:  # High-end GPU
                settings["num_workers"] = 8
                settings["pin_memory"] = True
                settings["persistent_workers"] = True
            elif gpu_memory >= 12:  # Mid-range GPU
                settings["num_workers"] = 6
                settings["pin_memory"] = True
                settings["persistent_workers"] = True
            elif gpu_memory >= 6:  # Entry-level GPU
                settings["num_workers"] = 4
                settings["pin_memory"] = True
            else:  # Low memory GPU
                settings["num_workers"] = 2
                settings["pin_memory"] = False
        
        # Enable optimizations
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        
    elif device == "mps":
        # MPS optimizations
        settings["num_workers"] = 4
        settings["pin_memory"] = False
        
    else:  # CPU
        # CPU optimizations
        cpu_cores = psutil.cpu_count(logical=False)
        settings["num_workers"] = min(cpu_cores, 8)
        settings["pin_memory"] = False
    
    # Adjust batch size if provided
    if batch_size and device.startswith("cuda"):
        gpu_id = int(device.split(":")[-1]) if ":" in device else 0
        device_info = get_device_info()
        
        if gpu_id < len(device_info["cuda"]["devices"]):
            gpu_memory = device_info["cuda"]["devices"][gpu_id].get("total_memory_gb", 0)
            
            # Suggest optimal batch size based on GPU memory
            if gpu_memory >= 24:
                suggested_batch = min(batch_size, 64)
            elif gpu_memory >= 12:
                suggested_batch = min(batch_size, 32)
            elif gpu_memory >= 6:
                suggested_batch = min(batch_size, 16)
            else:
                suggested_batch = min(batch_size, 8)
            
            if suggested_batch != batch_size:
                logger.warning(f"Suggested batch size {suggested_batch} for GPU with {gpu_memory}GB memory")
                settings["suggested_batch_size"] = suggested_batch
    
    return settings


def clear_gpu_memory(device: Optional[str] = None) -> None:
    """
    Clear GPU memory cache.
    
    Args:
        device: Device to clear (if None, clear all)
    """
    if torch.cuda.is_available():
        if device and device.startswith("cuda"):
            gpu_id = int(device.split(":")[-1]) if ":" in device else 0
            torch.cuda.set_device(gpu_id)
            torch.cuda.empty_cache()
            logger.info(f"Cleared GPU {gpu_id} memory cache")
        else:
            torch.cuda.empty_cache()
            logger.info("Cleared all GPU memory cache")


def monitor_gpu_memory(device: str) -> Dict[str, float]:
    """
    Monitor GPU memory usage.
    
    Args:
        device: CUDA device string
        
    Returns:
        Memory usage statistics in GB
    """
    if not device.startswith("cuda"):
        return {}
    
    gpu_id = int(device.split(":")[-1]) if ":" in device else 0
    
    if torch.cuda.is_available() and gpu_id < torch.cuda.device_count():
        torch.cuda.set_device(gpu_id)
        
        allocated = torch.cuda.memory_allocated(gpu_id) / (1024**3)
        reserved = torch.cuda.memory_reserved(gpu_id) / (1024**3)
        total = torch.cuda.get_device_properties(gpu_id).total_memory / (1024**3)
        
        return {
            "allocated_gb": round(allocated, 2),
            "reserved_gb": round(reserved, 2),
            "free_gb": round(total - reserved, 2),
            "total_gb": round(total, 2),
            "utilization_percent": round((reserved / total) * 100, 1)
        }
    
    return {}


def set_device_for_model(model, device: str) -> None:
    """
    Set device for a model with error handling.
    
    Args:
        model: PyTorch model
        device: Target device
    """
    try:
        model.to(device)
        logger.info(f"Model moved to device: {device}")
    except Exception as e:
        logger.error(f"Failed to move model to device {device}: {e}")
        # Fallback to CPU
        model.to("cpu")
        logger.info("Fallback: Model moved to CPU")


def log_device_status() -> None:
    """Log current device status for debugging."""
    device_info = get_device_info()
    
    logger.info("=== Device Status ===")
    logger.info(f"Platform: {device_info['platform']}")
    logger.info(f"CPU: {device_info['cpu']['model']} ({device_info['cpu']['cores']} cores)")
    logger.info(f"Memory: {device_info['memory']['available_gb']:.1f}GB available / "
               f"{device_info['memory']['total_gb']:.1f}GB total")
    
    if device_info["cuda"]["available"]:
        logger.info(f"CUDA: Available (version {device_info['cuda']['version']})")
        for gpu in device_info["cuda"]["devices"]:
            logger.info(f"  GPU {gpu['id']}: {gpu['name']} "
                       f"({gpu['memory_free_gb']:.1f}GB free / {gpu['total_memory_gb']:.1f}GB total)")
    else:
        logger.info("CUDA: Not available")
    
    if device_info["mps"]["available"]:
        logger.info("MPS: Available (Apple Silicon)")
    
    logger.info("====================")
