# YOLOv11-N Model Configuration
# Latest YOLO version with improved architecture

_target_: models.yolov11.YOLOv11

# Model architecture
architecture:
  name: "yolov11n"
  variant: "nano"
  yaml_path: "ultralytics/cfg/models/11/yolo11n.yaml"

# Model specifications
specs:
  input_size: [640, 640]
  num_classes: 1  # Will be overridden by dataset config
  channels: 3
  
# Performance characteristics
performance:
  params_m: 2.6      # Parameters in millions
  flops_g: 6.5       # FLOPs in billions
  latency_ms: 1.55   # Inference latency
  
# Architecture features
features:
  c2f_blocks: true
  sppf: true
  pan_fpn: true
  decoupled_head: true

# Training specific settings
training:
  pretrained: true
  freeze_backbone: false
  gradient_checkpointing: false

# Export settings
export:
  formats: ["pt", "onnx", "engine"]
  optimize: true
  half: true
