#!/usr/bin/env python3
"""
Project Migration Script

This script migrates the existing YOLOv13 project to the new structured format.
It organizes files, updates configurations, and preserves existing data.

Usage:
    python scripts/migrate_project.py
"""

import shutil
import json
from pathlib import Path
from typing import Dict, Any, List
import logging

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ProjectMigrator:
    """Handles migration of existing project to new structure."""
    
    def __init__(self, project_root: Path):
        """
        Initialize migrator.
        
        Args:
            project_root: Root directory of the project
        """
        self.project_root = project_root
        self.backup_dir = project_root / "backup_old_structure"
        
    def migrate(self) -> None:
        """Execute the complete migration process."""
        logger.info("Starting project migration...")
        
        # Create backup
        self.create_backup()
        
        # Migrate files
        self.migrate_datasets()
        self.migrate_models()
        self.migrate_scripts()
        self.migrate_results()
        self.migrate_configs()
        
        # Update configurations
        self.update_dataset_configs()
        
        # Clean up old files
        self.cleanup_old_files()
        
        logger.info("Project migration completed successfully!")
        logger.info(f"Backup created at: {self.backup_dir}")
    
    def create_backup(self) -> None:
        """Create backup of important existing files."""
        logger.info("Creating backup of existing files...")
        
        self.backup_dir.mkdir(exist_ok=True)
        
        # Files to backup
        backup_files = [
            "convert_aqua_to_yolo.py",
            "train_aqua.py", 
            "evaluate_model.py",
            "visualize_yolo_dataset.py",
            "README.md",
            "requirements.txt",
            "pyproject.toml"
        ]
        
        for file_name in backup_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                shutil.copy2(file_path, self.backup_dir / file_name)
                logger.info(f"Backed up: {file_name}")
    
    def migrate_datasets(self) -> None:
        """Migrate dataset-related files."""
        logger.info("Migrating datasets...")
        
        # Create new datasets directory structure
        datasets_dir = self.project_root / "datasets"
        datasets_dir.mkdir(exist_ok=True)
        
        # Move existing converter
        old_converter = self.project_root / "convert_aqua_to_yolo.py"
        if old_converter.exists():
            # The new converter is already created, so we'll keep the old one as reference
            shutil.copy2(old_converter, self.backup_dir / "convert_aqua_to_yolo_original.py")
        
        # Move visualization script
        old_visualizer = self.project_root / "visualize_yolo_dataset.py"
        if old_visualizer.exists():
            new_visualizer = datasets_dir / "visualizers" / "yolo_visualizer.py"
            new_visualizer.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(old_visualizer), str(new_visualizer))
            logger.info("Moved dataset visualizer")
    
    def migrate_models(self) -> None:
        """Migrate model-related files."""
        logger.info("Migrating models...")
        
        # The ultralytics directory should stay in place as it contains the YOLOv13 implementation
        ultralytics_dir = self.project_root / "ultralytics"
        if ultralytics_dir.exists():
            logger.info("Keeping ultralytics directory in place")
            # We could create a symlink or reference in the new models structure
            models_dir = self.project_root / "models"
            models_dir.mkdir(exist_ok=True)
            
            # Create a reference file
            reference_file = models_dir / "ultralytics_reference.txt"
            with open(reference_file, 'w') as f:
                f.write("The ultralytics directory contains the YOLOv13 implementation.\n")
                f.write("It should remain in the project root for compatibility.\n")
                f.write("Path: ../ultralytics/\n")
    
    def migrate_scripts(self) -> None:
        """Migrate existing scripts."""
        logger.info("Migrating scripts...")
        
        scripts_dir = self.project_root / "scripts"
        scripts_dir.mkdir(exist_ok=True)
        
        # Move training script
        old_train = self.project_root / "train_aqua.py"
        if old_train.exists():
            shutil.copy2(old_train, self.backup_dir / "train_aqua_original.py")
            logger.info("Backed up original training script")
        
        # Move evaluation script
        old_eval = self.project_root / "evaluate_model.py"
        if old_eval.exists():
            new_eval = scripts_dir / "evaluate_legacy.py"
            shutil.copy2(old_eval, new_eval)
            logger.info("Copied evaluation script to scripts directory")
    
    def migrate_results(self) -> None:
        """Migrate existing results and experiments."""
        logger.info("Migrating results...")
        
        experiments_dir = self.project_root / "experiments"
        experiments_dir.mkdir(exist_ok=True)
        
        # Move runs directory
        old_runs = self.project_root / "runs"
        if old_runs.exists():
            new_runs = experiments_dir / "runs"
            if not new_runs.exists():
                shutil.move(str(old_runs), str(new_runs))
                logger.info("Moved runs directory to experiments")
        
        # Move evaluation results
        old_eval_results = self.project_root / "evaluation_results"
        if old_eval_results.exists():
            new_eval_results = experiments_dir / "evaluations"
            if not new_eval_results.exists():
                shutil.move(str(old_eval_results), str(new_eval_results))
                logger.info("Moved evaluation results to experiments")
    
    def migrate_configs(self) -> None:
        """Migrate and update configuration files."""
        logger.info("Migrating configurations...")
        
        # The new configs are already created, so we'll preserve any custom configs
        configs_dir = self.project_root / "configs"
        
        # Check for any existing YAML configs and preserve them
        for yaml_file in self.project_root.glob("*.yaml"):
            if yaml_file.name not in ["config.yaml"]:  # Don't overwrite our new main config
                backup_path = self.backup_dir / yaml_file.name
                shutil.copy2(yaml_file, backup_path)
                logger.info(f"Backed up config: {yaml_file.name}")
    
    def update_dataset_configs(self) -> None:
        """Update dataset configurations with actual paths."""
        logger.info("Updating dataset configurations...")
        
        # Update Aqua dataset config with actual paths
        aqua_config_path = self.project_root / "configs" / "datasets" / "aqua.yaml"
        
        if aqua_config_path.exists():
            # Check if the actual dataset exists and update paths
            datasets_root = self.project_root / "Datasets"
            
            if datasets_root.exists():
                # Update the config with correct paths
                import yaml
                
                with open(aqua_config_path, 'r') as f:
                    config = yaml.safe_load(f)
                
                # Update paths to match existing structure
                aqua_yolo_path = datasets_root / "Aqua_YOLO"
                if aqua_yolo_path.exists():
                    config['path'] = str(aqua_yolo_path)
                    logger.info("Updated Aqua dataset path in config")
                
                # Check for actual dataset statistics
                if (aqua_yolo_path / "images" / "train").exists():
                    train_images = list((aqua_yolo_path / "images" / "train").glob("*.jpg"))
                    train_images.extend(list((aqua_yolo_path / "images" / "train").glob("*.png")))
                    config['stats']['train_images'] = len(train_images)
                
                if (aqua_yolo_path / "images" / "val").exists():
                    val_images = list((aqua_yolo_path / "images" / "val").glob("*.jpg"))
                    val_images.extend(list((aqua_yolo_path / "images" / "val").glob("*.png")))
                    config['stats']['val_images'] = len(val_images)
                
                # Save updated config
                with open(aqua_config_path, 'w') as f:
                    yaml.dump(config, f, default_flow_style=False, indent=2)
                
                logger.info("Updated Aqua dataset configuration")
    
    def cleanup_old_files(self) -> None:
        """Clean up old files that are no longer needed."""
        logger.info("Cleaning up old files...")
        
        # Files to remove (they're backed up)
        cleanup_files = [
            "convert_aqua_to_yolo.py",
            "train_aqua.py"
        ]
        
        for file_name in cleanup_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Removed old file: {file_name}")
    
    def create_migration_report(self) -> None:
        """Create a migration report."""
        report_path = self.project_root / "MIGRATION_REPORT.md"
        
        report_content = """# Project Migration Report

## Overview
This project has been migrated to a new structured format for better organization and extensibility.

## Changes Made

### Directory Structure
- Created new organized directory structure
- Moved existing files to appropriate locations
- Preserved all original files in backup

### New Features
- Unified configuration management with Hydra
- Model registry system for multiple YOLO versions
- Standardized training and evaluation interfaces
- Improved dataset conversion system

### Preserved Files
- All original scripts backed up in `backup_old_structure/`
- Existing datasets and results preserved
- Training runs and evaluation results maintained

### Migration Actions
1. Backed up original files
2. Created new directory structure
3. Moved datasets and results to new locations
4. Updated configuration files
5. Created new unified scripts

## Usage After Migration

### Training
```bash
python scripts/train.py model=yolov13n dataset=aqua
```

### Dataset Conversion
```bash
python scripts/convert_dataset.py dataset=aqua
```

### Evaluation
```bash
python scripts/evaluate.py model=yolov13n dataset=aqua weights=path/to/weights.pt
```

## Rollback
If needed, original files can be restored from the `backup_old_structure/` directory.
"""
        
        with open(report_path, 'w') as f:
            f.write(report_content)
        
        logger.info(f"Created migration report: {report_path}")


def main():
    """Main migration function."""
    project_root = Path(__file__).parent.parent
    
    migrator = ProjectMigrator(project_root)
    
    try:
        migrator.migrate()
        migrator.create_migration_report()
        
        print("\n" + "="*50)
        print("MIGRATION COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"Backup created at: {migrator.backup_dir}")
        print("\nNext steps:")
        print("1. Review the new directory structure")
        print("2. Test the new training script: python scripts/train.py model=yolov13n dataset=aqua")
        print("3. Check MIGRATION_REPORT.md for detailed information")
        print("="*50)
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        print(f"\nMigration failed: {e}")
        print("Please check the logs and try again.")


if __name__ == "__main__":
    main()
