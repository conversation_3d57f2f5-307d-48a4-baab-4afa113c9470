"""
YOLOv13 Model Implementation

This module implements the YOLOv13 model with HyperACE and FullPAD innovations.
"""

from typing import Dict, Any, Optional, Union
import logging
from pathlib import Path

try:
    from ultralytics import YOLO
except ImportError:
    raise ImportError("ultralytics package is required. Install with: pip install ultralytics")

from ..registry import BaseModel

logger = logging.getLogger(__name__)


class YOLOv13(BaseModel):
    """
    YOLOv13 model implementation with HyperACE and FullPAD innovations.
    
    Features:
    - HyperACE: Hypergraph-based Adaptive Correlation Enhancement
    - FullPAD: Full-Pipeline Aggregation-and-Distribution paradigm
    - Lightweight DS-based modules (DSConv, DSC3k2, A2C2f)
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_path = None
        
    def load(self, weights: Optional[str] = None) -> None:
        """
        Load YOLOv13 model with optional pretrained weights.
        
        Args:
            weights: Path to weights file or model name
        """
        try:
            # Get model configuration
            arch_config = self.config.get("architecture", {})
            yaml_path = arch_config.get("yaml_path", "ultralytics/cfg/models/v13/yolov13.yaml")
            
            if weights:
                # Load with specific weights
                self.model = YOLO(weights)
                self.model_path = weights
                logger.info(f"Loaded YOLOv13 with weights: {weights}")
            else:
                # Load from YAML configuration
                if Path(yaml_path).exists():
                    self.model = YOLO(yaml_path)
                    self.model_path = yaml_path
                    logger.info(f"Loaded YOLOv13 from config: {yaml_path}")
                else:
                    # Fallback to default model name
                    model_name = arch_config.get("name", "yolov13n")
                    self.model = YOLO(f"{model_name}.pt")
                    self.model_path = f"{model_name}.pt"
                    logger.info(f"Loaded YOLOv13 default model: {model_name}")
            
            self.is_loaded = True
            
        except Exception as e:
            logger.error(f"Failed to load YOLOv13 model: {e}")
            raise
    
    def train(self, **kwargs) -> Any:
        """
        Train the YOLOv13 model.
        
        Args:
            **kwargs: Training arguments
            
        Returns:
            Training results
        """
        if not self.is_loaded:
            self.load()
        
        # Set default training parameters
        train_args = {
            "epochs": 100,
            "imgsz": 640,
            "batch": 16,
            "device": "auto",
            **kwargs
        }
        
        logger.info(f"Starting YOLOv13 training with args: {train_args}")
        
        try:
            results = self.model.train(**train_args)
            logger.info("YOLOv13 training completed successfully")
            return results
        except Exception as e:
            logger.error(f"YOLOv13 training failed: {e}")
            raise
    
    def predict(self, source: str, **kwargs) -> Any:
        """
        Run inference with YOLOv13 model.
        
        Args:
            source: Input source (image, video, directory, etc.)
            **kwargs: Prediction arguments
            
        Returns:
            Prediction results
        """
        if not self.is_loaded:
            self.load()
        
        # Set default prediction parameters
        pred_args = {
            "conf": 0.25,
            "iou": 0.7,
            "imgsz": 640,
            "device": "auto",
            **kwargs
        }
        
        logger.info(f"Running YOLOv13 inference on: {source}")
        
        try:
            results = self.model.predict(source, **pred_args)
            return results
        except Exception as e:
            logger.error(f"YOLOv13 prediction failed: {e}")
            raise
    
    def val(self, **kwargs) -> Any:
        """
        Validate the YOLOv13 model.
        
        Args:
            **kwargs: Validation arguments
            
        Returns:
            Validation results
        """
        if not self.is_loaded:
            self.load()
        
        # Set default validation parameters
        val_args = {
            "imgsz": 640,
            "batch": 32,
            "device": "auto",
            **kwargs
        }
        
        logger.info("Running YOLOv13 validation")
        
        try:
            results = self.model.val(**val_args)
            logger.info("YOLOv13 validation completed successfully")
            return results
        except Exception as e:
            logger.error(f"YOLOv13 validation failed: {e}")
            raise
    
    def export(self, format: str = "onnx", **kwargs) -> str:
        """
        Export YOLOv13 model to specified format.
        
        Args:
            format: Export format (onnx, engine, coreml, etc.)
            **kwargs: Export arguments
            
        Returns:
            Path to exported model
        """
        if not self.is_loaded:
            self.load()
        
        # Set default export parameters
        export_args = {
            "format": format,
            "imgsz": 640,
            "half": True,
            "optimize": True,
            **kwargs
        }
        
        logger.info(f"Exporting YOLOv13 model to {format} format")
        
        try:
            export_path = self.model.export(**export_args)
            logger.info(f"YOLOv13 model exported to: {export_path}")
            return export_path
        except Exception as e:
            logger.error(f"YOLOv13 export failed: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get detailed information about the YOLOv13 model.
        
        Returns:
            Model information dictionary
        """
        if not self.is_loaded:
            return {"status": "not_loaded"}
        
        try:
            # Get model info
            info = {
                "name": self.name,
                "variant": self.variant,
                "model_path": str(self.model_path),
                "architecture": self.config.get("architecture", {}),
                "performance": self.config.get("performance", {}),
                "hyperace": self.config.get("hyperace", {}),
                "fullpad": self.config.get("fullpad", {}),
                "lightweight": self.config.get("lightweight", {}),
                "status": "loaded"
            }
            
            # Add model-specific info if available
            if hasattr(self.model, 'info'):
                try:
                    self.model.info(verbose=False)
                except:
                    pass
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get YOLOv13 model info: {e}")
            return {"status": "error", "error": str(e)}
    
    @property
    def hyperace_enabled(self) -> bool:
        """Check if HyperACE is enabled."""
        return self.config.get("hyperace", {}).get("enabled", False)
    
    @property
    def fullpad_enabled(self) -> bool:
        """Check if FullPAD is enabled."""
        return self.config.get("fullpad", {}).get("enabled", False)
    
    @property
    def lightweight_modules(self) -> Dict[str, bool]:
        """Get lightweight modules configuration."""
        return self.config.get("lightweight", {})
