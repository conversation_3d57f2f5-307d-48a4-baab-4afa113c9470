# Standard Evaluation Configuration
# Comprehensive evaluation settings for model assessment

# Evaluation parameters
task: "detect"
split: "test"  # Dataset split to evaluate on
imgsz: 640     # Image size for evaluation

# Detection thresholds
thresholds:
  conf: 0.25     # Confidence threshold
  iou: 0.7       # IoU threshold for NMS
  max_det: 300   # Maximum detections per image

# Metrics to compute
metrics:
  detection:
    - "mAP@0.5"      # mAP at IoU=0.5
    - "mAP@0.5:0.95" # mAP at IoU=0.5:0.95
    - "precision"    # Precision
    - "recall"       # Recall
    - "f1"          # F1 score
  
  performance:
    - "fps"          # Frames per second
    - "latency"      # Inference latency (ms)
    - "memory"       # Memory usage (MB)
    - "flops"        # FLOPs
    - "params"       # Number of parameters
    - "model_size"   # Model file size (MB)

# Evaluation settings
settings:
  device: null       # Device (auto-detected if null)
  workers: 8         # Number of dataloader workers
  batch_size: 32     # Batch size for evaluation
  verbose: true      # Verbose output
  save_txt: true     # Save results to text files
  save_conf: true    # Save confidence scores
  save_json: true    # Save results to JSON
  plots: true        # Generate plots
  
# Visualization settings
visualization:
  save_plots: true
  plot_types:
    - "confusion_matrix"
    - "pr_curve"
    - "f1_curve"
    - "results"
    - "predictions"
  
  plot_settings:
    dpi: 300
    format: "png"
    bbox_inches: "tight"

# Output settings
output:
  save_dir: "experiments/evaluations"
  save_predictions: true
  save_features: false
  export_format: ["json", "csv"]

# Benchmark settings
benchmark:
  warmup_runs: 5     # Number of warmup runs
  timing_runs: 100   # Number of timing runs
  memory_profile: true
  
# Advanced settings
advanced:
  augment: false     # Test-time augmentation
  agnostic_nms: false # Class-agnostic NMS
  single_cls: false  # Treat as single class
  half: false        # Use FP16 inference
  dnn: false         # Use OpenCV DNN backend
