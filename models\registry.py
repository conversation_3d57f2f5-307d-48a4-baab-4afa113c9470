"""
Model Registry System for YOLO Baseline Platform

This module provides a centralized registry for managing different YOLO model versions
and their configurations. It supports dynamic model loading and instantiation.
"""

from typing import Dict, Any, Type, Optional, List
from abc import ABC, abstractmethod
import importlib
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class BaseModel(ABC):
    """Base class for all YOLO models in the platform."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model = None
        self.is_loaded = False
    
    @abstractmethod
    def load(self, weights: Optional[str] = None) -> None:
        """Load the model with optional pretrained weights."""
        pass
    
    @abstractmethod
    def train(self, **kwargs) -> Any:
        """Train the model."""
        pass
    
    @abstractmethod
    def predict(self, source: str, **kwargs) -> Any:
        """Run inference on the given source."""
        pass
    
    @abstractmethod
    def val(self, **kwargs) -> Any:
        """Validate the model."""
        pass
    
    @abstractmethod
    def export(self, format: str = "onnx", **kwargs) -> str:
        """Export the model to specified format."""
        pass
    
    @property
    def name(self) -> str:
        """Get model name."""
        return self.config.get("architecture", {}).get("name", "unknown")
    
    @property
    def variant(self) -> str:
        """Get model variant."""
        return self.config.get("architecture", {}).get("variant", "unknown")


class ModelRegistry:
    """Registry for managing YOLO model implementations."""
    
    def __init__(self):
        self._models: Dict[str, Type[BaseModel]] = {}
        self._configs: Dict[str, Dict[str, Any]] = {}
        self._aliases: Dict[str, str] = {}
        
    def register(self, name: str, model_class: Type[BaseModel], 
                 config: Optional[Dict[str, Any]] = None,
                 aliases: Optional[List[str]] = None) -> None:
        """
        Register a model class with the registry.
        
        Args:
            name: Model name (e.g., 'yolov13n')
            model_class: Model class implementing BaseModel
            config: Optional model configuration
            aliases: Optional list of aliases for the model
        """
        if not issubclass(model_class, BaseModel):
            raise ValueError(f"Model class {model_class} must inherit from BaseModel")
        
        self._models[name] = model_class
        if config:
            self._configs[name] = config
            
        # Register aliases
        if aliases:
            for alias in aliases:
                self._aliases[alias] = name
                
        logger.info(f"Registered model: {name}")
    
    def get_model(self, name: str, config: Optional[Dict[str, Any]] = None) -> BaseModel:
        """
        Get a model instance by name.
        
        Args:
            name: Model name or alias
            config: Optional configuration override
            
        Returns:
            Model instance
        """
        # Resolve alias
        actual_name = self._aliases.get(name, name)
        
        if actual_name not in self._models:
            raise ValueError(f"Model '{name}' not found in registry. "
                           f"Available models: {list(self._models.keys())}")
        
        model_class = self._models[actual_name]
        
        # Use provided config or default config
        model_config = config or self._configs.get(actual_name, {})
        
        return model_class(model_config)
    
    def list_models(self) -> List[str]:
        """List all registered model names."""
        return list(self._models.keys())
    
    def list_aliases(self) -> Dict[str, str]:
        """List all registered aliases."""
        return self._aliases.copy()
    
    def has_model(self, name: str) -> bool:
        """Check if a model is registered."""
        actual_name = self._aliases.get(name, name)
        return actual_name in self._models
    
    def get_model_info(self, name: str) -> Dict[str, Any]:
        """Get information about a registered model."""
        actual_name = self._aliases.get(name, name)
        
        if actual_name not in self._models:
            raise ValueError(f"Model '{name}' not found in registry")
        
        model_class = self._models[actual_name]
        config = self._configs.get(actual_name, {})
        
        return {
            "name": actual_name,
            "class": model_class.__name__,
            "module": model_class.__module__,
            "config": config,
            "aliases": [alias for alias, target in self._aliases.items() 
                       if target == actual_name]
        }


# Global model registry instance
model_registry = ModelRegistry()


def register_model(name: str, config: Optional[Dict[str, Any]] = None,
                  aliases: Optional[List[str]] = None):
    """
    Decorator for registering model classes.
    
    Args:
        name: Model name
        config: Optional model configuration
        aliases: Optional list of aliases
    """
    def decorator(model_class: Type[BaseModel]):
        model_registry.register(name, model_class, config, aliases)
        return model_class
    return decorator


def get_model(name: str, config: Optional[Dict[str, Any]] = None) -> BaseModel:
    """
    Convenience function to get a model from the registry.
    
    Args:
        name: Model name or alias
        config: Optional configuration override
        
    Returns:
        Model instance
    """
    return model_registry.get_model(name, config)


def list_available_models() -> List[str]:
    """List all available models."""
    return model_registry.list_models()


def auto_discover_models(models_dir: str = "models") -> None:
    """
    Automatically discover and import model modules.
    
    Args:
        models_dir: Directory containing model modules
    """
    models_path = Path(models_dir)
    
    if not models_path.exists():
        logger.warning(f"Models directory {models_dir} not found")
        return
    
    # Import all Python files in model subdirectories
    for model_dir in models_path.iterdir():
        if model_dir.is_dir() and not model_dir.name.startswith('_'):
            try:
                # Try to import the model module
                module_name = f"{models_dir}.{model_dir.name}"
                importlib.import_module(module_name)
                logger.info(f"Imported model module: {module_name}")
            except ImportError as e:
                logger.warning(f"Failed to import {module_name}: {e}")


# Auto-discover models on import
try:
    auto_discover_models()
except Exception as e:
    logger.warning(f"Auto-discovery failed: {e}")
