# Quick Start Guide - YOLO Baseline Platform

This guide will help you get started with the newly restructured YOLO Baseline Platform.

## 🚀 Migration from Old Structure

If you're migrating from the old project structure, run the migration script first:

```bash
python scripts/migrate_project.py
```

This will:
- Backup your existing files
- Organize them into the new structure
- Preserve all your data and results
- Create a detailed migration report

## 📦 Installation

1. **Install dependencies:**
```bash
pip install -r requirements_new.txt
```

2. **Verify installation:**
```bash
python -c "import torch; import ultralytics; print('Installation successful!')"
```

## 🗂️ Project Structure Overview

```
yolo-baseline/
├── configs/              # All configuration files
│   ├── models/          # Model configurations (yolov8, yolov11, yolov13, etc.)
│   ├── datasets/        # Dataset configurations
│   ├── training/        # Training configurations
│   └── config.yaml      # Main configuration
├── datasets/            # Dataset management
│   ├── converters/      # Format conversion tools
│   └── visualizers/     # Visualization tools
├── models/              # Model registry and implementations
├── scripts/             # Main execution scripts
├── training/            # Training modules
├── utils/               # Utility functions
└── experiments/         # Results and outputs
```

## 🎯 Basic Usage

### 1. Convert Dataset (if needed)

If you have a new dataset or need to reconvert:

```bash
# Convert Aqua dataset from Unity Solo format to YOLO
python scripts/convert_dataset.py dataset=aqua

# Convert with custom split ratios
python scripts/convert_dataset.py dataset=aqua converter.split_ratios.train=0.8 converter.split_ratios.val=0.15 converter.split_ratios.test=0.05
```

### 2. Train a Model

```bash
# Basic training with YOLOv13-N on Aqua dataset
python scripts/train.py model=yolov13n dataset=aqua

# Training with custom parameters
python scripts/train.py model=yolov13n dataset=aqua training.epochs=200 training.batch_size=32

# Training different models
python scripts/train.py model=yolov11n dataset=aqua
python scripts/train.py model=yolov8n dataset=aqua
```

### 3. Evaluate a Model

```bash
# Evaluate trained model
python scripts/evaluate.py model=yolov13n dataset=aqua weights=experiments/runs/train/exp/weights/best.pt

# Compare multiple models
python scripts/compare.py models=[yolov8n,yolov11n,yolov13n] dataset=aqua
```

## 🔧 Configuration System

The platform uses Hydra for configuration management. You can:

### Override any parameter from command line:
```bash
python scripts/train.py model=yolov13n dataset=aqua training.epochs=300 training.optimizer.lr0=0.002
```

### Use different configuration files:
```bash
python scripts/train.py model=yolov13s dataset=aqua training=fast
```

### Create custom configurations:
Create new YAML files in the appropriate `configs/` subdirectory.

## 📊 Available Models

The platform supports multiple YOLO versions:

- **YOLOv8**: `yolov8n`, `yolov8s`, `yolov8m`, `yolov8l`, `yolov8x`
- **YOLOv11**: `yolov11n`, `yolov11s`, `yolov11m`, `yolov11l`, `yolov11x`
- **YOLOv13**: `yolov13n`, `yolov13s`, `yolov13m`, `yolov13l`, `yolov13x`

## 🗃️ Dataset Support

Currently supported datasets:
- **Aqua Satellite**: Unity Solo format → YOLO conversion
- **Custom**: Easy to add new dataset converters

## 📈 Experiment Tracking

All experiments are automatically tracked:

```
experiments/
├── runs/                # Training runs
│   └── train/
│       ├── exp1/        # First experiment
│       ├── exp2/        # Second experiment
│       └── ...
├── evaluations/         # Evaluation results
└── comparisons/         # Model comparisons
```

## 🔍 Monitoring and Logs

Logs are automatically saved to:
- `logs/yolo_baseline.log` - Main application log
- `logs/errors.log` - Error log
- `experiments/{experiment_name}/logs/` - Experiment-specific logs

## 🛠️ Advanced Usage

### Custom Model Configuration

Create a new model config in `configs/models/`:

```yaml
# configs/models/custom_yolo.yaml
_target_: models.yolov13.YOLOv13

architecture:
  name: "custom_yolo"
  variant: "custom"
  yaml_path: "path/to/custom/model.yaml"

specs:
  input_size: [640, 640]
  num_classes: 1
  channels: 3

training:
  pretrained: true
  freeze_backbone: false
```

### Custom Dataset

Create a new dataset config in `configs/datasets/`:

```yaml
# configs/datasets/custom_dataset.yaml
name: "custom_dataset"
description: "My custom dataset"
task: "detect"
format: "yolo"

path: "Custom_Dataset"
train: "images/train"
val: "images/val"
test: "images/test"

nc: 3  # Number of classes
names:
  0: "class1"
  1: "class2"
  2: "class3"
```

### Hyperparameter Sweeps

Use Hydra's sweep functionality:

```bash
python scripts/train.py -m model=yolov13n dataset=aqua training.optimizer.lr0=0.001,0.01,0.1 training.batch_size=16,32
```

## 🐛 Troubleshooting

### Common Issues

1. **CUDA out of memory**: Reduce batch size
   ```bash
   python scripts/train.py model=yolov13n dataset=aqua training.batch_size=8
   ```

2. **Dataset not found**: Ensure dataset is converted
   ```bash
   python scripts/convert_dataset.py dataset=aqua
   ```

3. **Model not found**: Check available models
   ```python
   from models.registry import list_available_models
   print(list_available_models())
   ```

### Getting Help

1. Check logs in `logs/` directory
2. Review experiment logs in `experiments/{experiment_name}/logs/`
3. Check the migration report: `MIGRATION_REPORT.md`

## 📚 Next Steps

1. **Explore configurations**: Look at files in `configs/` to understand available options
2. **Add new models**: Implement new YOLO versions in `models/`
3. **Create custom datasets**: Add new converters in `datasets/converters/`
4. **Extend evaluation**: Add new metrics in `evaluation/`

## 🔄 Rollback (if needed)

If you need to revert to the old structure:

1. All original files are backed up in `backup_old_structure/`
2. Simply copy them back to the project root
3. The old scripts will work as before

---

**Happy training! 🎉**

For more detailed information, see the full README_NEW.md file.
