#!/usr/bin/env python3
"""
YOLOv13 Model Evaluation and Prediction Script

This script provides the following functions:
1. Evaluate model performance on test set
2. Predict on single image
3. Visualize test set detection results
4. Generate detailed performance reports

Author: AI Assistant
Date: 2025-01-16
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import yaml
import json
import argparse
from typing import List, Dict, Tuple, Optional
import pandas as pd
from ultralytics import YOLO
import torch
from PIL import Image, ImageDraw, ImageFont
import matplotlib
matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']

class YOLOv13Evaluator:
    """YOLOv13 Model Evaluator"""
    
    def __init__(self, model_path: str, dataset_path: str):
        """
        Initialize evaluator
        
        Args:
            model_path (str): Path to trained model
            dataset_path (str): Path to dataset
        """
        self.model_path = Path(model_path)
        self.dataset_path = Path(dataset_path)
        self.model = None
        self.class_names = {}
        self.colors = {}
        
        # Check model and dataset paths
        self._check_paths()
        
        # Load model
        self._load_model()
        
        # Load dataset configuration
        self._load_dataset_config()
        
        # Generate class colors
        self._generate_colors()
    
    def _check_paths(self):
        """Check model and dataset paths"""
        if not self.model_path.exists():
            raise FileNotFoundError(f"Model file not found: {self.model_path}")
        
        if not self.dataset_path.exists():
            raise FileNotFoundError(f"Dataset directory not found: {self.dataset_path}")
        
        # Check test set
        test_images_dir = self.dataset_path / "images" / "test"
        test_labels_dir = self.dataset_path / "labels" / "test"
        
        if not test_images_dir.exists():
            raise FileNotFoundError(f"Test images directory not found: {test_images_dir}")
        
        if not test_labels_dir.exists():
            raise FileNotFoundError(f"Test labels directory not found: {test_labels_dir}")
    
    def _load_model(self):
        """Load trained model"""
        print(f"📦 Loading model: {self.model_path}")
        try:
            self.model = YOLO(str(self.model_path))
            print("✅ Model loaded successfully")
        except Exception as e:
            raise RuntimeError(f"Failed to load model: {e}")
    
    def _load_dataset_config(self):
        """Load dataset configuration"""
        yaml_file = self.dataset_path / "aqua_dataset.yaml"
        if not yaml_file.exists():
            print("⚠️  Dataset config file not found, using default class names")
            self.class_names = {0: "Satellite"}
            return
        
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            self.class_names = config.get('names', {0: "Satellite"})
            print(f"📋 Loaded dataset config: {len(self.class_names)} classes")
            
        except Exception as e:
            print(f"⚠️  Failed to load config file: {e}, using default settings")
            self.class_names = {0: "Satellite"}
    
    def _generate_colors(self):
        """Generate different colors for each class"""
        np.random.seed(42)
        for class_id in self.class_names.keys():
            color = np.random.randint(0, 255, 3)
            self.colors[class_id] = tuple(map(int, color))
    
    def evaluate_on_testset(self, conf_threshold=0.5, iou_threshold=0.7, save_results=True):
        """
        Evaluate model performance on test set
        
        Args:
            conf_threshold (float): Confidence threshold
            iou_threshold (float): IoU threshold
            save_results (bool): Whether to save results
            
        Returns:
            dict: Evaluation results
        """
        print("\n" + "=" * 80)
        print("🔍 Evaluating Model Performance on Test Set")
        print("=" * 80)
        
        # Get dataset config file path
        dataset_yaml = self.dataset_path / "aqua_dataset.yaml"
        
        # Run validation
        print("📊 Running model validation...")
        results = self.model.val(
            data=str(dataset_yaml),
            split='test',
            conf=conf_threshold,
            iou=iou_threshold,
            save_json=save_results,
            save_hybrid=False,  # Disable to avoid mAP calculation issues
            plots=save_results,
            verbose=True
        )
        
        # Extract key metrics
        metrics = {
            'mAP50-95': float(results.box.map),
            'mAP50': float(results.box.map50),
            'mAP75': float(results.box.map75),
            'precision': float(results.box.mp),
            'recall': float(results.box.mr),
            'f1_score': 2 * float(results.box.mp) * float(results.box.mr) / (float(results.box.mp) + float(results.box.mr)) if (float(results.box.mp) + float(results.box.mr)) > 0 else 0
        }
        
        # Display results
        print("\n📈 Test Set Evaluation Results:")
        print("-" * 50)
        print(f"mAP50-95:   {metrics['mAP50-95']:.4f}")
        print(f"mAP50:      {metrics['mAP50']:.4f}")
        print(f"mAP75:      {metrics['mAP75']:.4f}")
        print(f"Precision:  {metrics['precision']:.4f}")
        print(f"Recall:     {metrics['recall']:.4f}")
        print(f"F1-Score:   {metrics['f1_score']:.4f}")
        print("-" * 50)
        
        # Performance analysis
        self._analyze_performance(metrics)
        
        # Save evaluation report
        if save_results:
            self._save_evaluation_report(metrics, conf_threshold, iou_threshold)
        
        return metrics
    
    def _analyze_performance(self, metrics: dict):
        """Analyze and provide insights on model performance"""
        print("\n🎯 Performance Analysis:")
        
        # mAP analysis
        if metrics['mAP50'] > 0.9:
            print("✅ Excellent mAP50 performance (>0.90)")
        elif metrics['mAP50'] > 0.7:
            print("✅ Good mAP50 performance (0.70-0.90)")
        elif metrics['mAP50'] > 0.5:
            print("⚠️  Moderate mAP50 performance (0.50-0.70)")
        else:
            print("❌ Poor mAP50 performance (<0.50)")
        
        # Precision vs Recall balance
        if abs(metrics['precision'] - metrics['recall']) < 0.1:
            print("✅ Well-balanced precision and recall")
        elif metrics['precision'] > metrics['recall']:
            print("⚠️  High precision, lower recall - model is conservative")
        else:
            print("⚠️  High recall, lower precision - model detects more but less accurate")
        
        # Recommendations
        print("\n💡 Recommendations:")
        if metrics['mAP50'] < 0.8:
            print("• Consider training longer or with larger model")
            print("• Review data augmentation strategies")
        if metrics['recall'] < 0.7:
            print("• Lower confidence threshold to detect more objects")
        if metrics['precision'] < 0.8:
            print("• Increase confidence threshold to reduce false positives")
    
    def _save_evaluation_report(self, metrics: dict, conf_threshold: float, iou_threshold: float):
        """Save evaluation report"""
        report_dir = Path("evaluation_results")
        report_dir.mkdir(exist_ok=True)
        
        # Create detailed report
        report = {
            "model_path": str(self.model_path),
            "dataset_path": str(self.dataset_path),
            "evaluation_settings": {
                "confidence_threshold": conf_threshold,
                "iou_threshold": iou_threshold
            },
            "metrics": metrics,
            "class_names": self.class_names
        }
        
        # Save JSON report
        report_file = report_dir / "evaluation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📁 Evaluation report saved: {report_file}")
    
    def predict_single_image(self, image_path: str, conf_threshold=0.5, save_result=True, show_result=True):
        """
        Predict on single image
        
        Args:
            image_path (str): Image path
            conf_threshold (float): Confidence threshold
            save_result (bool): Whether to save result
            show_result (bool): Whether to show result
            
        Returns:
            tuple: (prediction result, visualization image)
        """
        image_path = Path(image_path)
        if not image_path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        print(f"\n🖼️  Predicting image: {image_path.name}")
        
        # Run prediction
        results = self.model.predict(
            source=str(image_path),
            conf=conf_threshold,
            save=save_result,
            verbose=False
        )
        
        # Get first result
        result = results[0]
        
        # Display detection results
        if len(result.boxes) > 0:
            print(f"✅ Detected {len(result.boxes)} objects:")
            
            for i, box in enumerate(result.boxes):
                class_id = int(box.cls[0])
                confidence = float(box.conf[0])
                class_name = self.class_names.get(class_id, f"Class_{class_id}")
                
                print(f"   Object {i+1}: {class_name} (confidence: {confidence:.3f})")
        else:
            print("❌ No objects detected")
        
        # Create visualization image
        vis_image = self._visualize_prediction(image_path, result)
        
        # Show result
        if show_result:
            self._show_image(vis_image, f"Prediction Result - {image_path.name}")
        
        return result, vis_image
    
    def _visualize_prediction(self, image_path: Path, result) -> np.ndarray:
        """Visualize prediction results"""
        # Read original image
        image = cv2.imread(str(image_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Draw detection boxes
        for box in result.boxes:
            # Get bounding box coordinates
            x1, y1, x2, y2 = map(int, box.xyxy[0])
            class_id = int(box.cls[0])
            confidence = float(box.conf[0])
            
            # Get class name and color
            class_name = self.class_names.get(class_id, f"Class_{class_id}")
            color = self.colors.get(class_id, (255, 0, 0))
            
            # Draw bounding box
            cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
            
            # Draw label
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # Draw label background
            cv2.rectangle(image, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            
            # Draw label text
            cv2.putText(image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return image
    
    def _show_image(self, image: np.ndarray, title: str = "Image"):
        """Display image"""
        plt.figure(figsize=(12, 8))
        plt.imshow(image)
        plt.title(title, fontsize=14)
        plt.axis('off')
        plt.tight_layout()
        plt.show()
    
    def visualize_test_results(self, num_samples=9, conf_threshold=0.5, save_grid=True):
        """
        Visualize test set detection results
        
        Args:
            num_samples (int): Number of samples to display
            conf_threshold (float): Confidence threshold
            save_grid (bool): Whether to save grid image
        """
        print(f"\n🎨 Visualizing test set results (showing {num_samples} samples)")
        
        # Get test set images
        test_images_dir = self.dataset_path / "images" / "test"
        test_images = list(test_images_dir.glob("*.png")) + list(test_images_dir.glob("*.jpg"))
        
        if len(test_images) == 0:
            print("❌ No images found in test set")
            return
        
        # Randomly select samples
        if len(test_images) > num_samples:
            import random
            test_images = random.sample(test_images, num_samples)
        
        # Calculate grid size
        cols = 3
        rows = (len(test_images) + cols - 1) // cols
        
        # Create figure
        fig, axes = plt.subplots(rows, cols, figsize=(15, 5 * rows))
        if rows == 1:
            axes = axes.reshape(1, -1)
        elif cols == 1:
            axes = axes.reshape(-1, 1)
        
        print("🔄 Processing test images...")
        
        detection_stats = {"detected": 0, "not_detected": 0}
        
        for i, img_path in enumerate(test_images):
            row = i // cols
            col = i % cols
            
            # Predict
            result, vis_image = self.predict_single_image(
                img_path, conf_threshold=conf_threshold, 
                save_result=False, show_result=False
            )
            
            # Update statistics
            if len(result.boxes) > 0:
                detection_stats["detected"] += 1
            else:
                detection_stats["not_detected"] += 1
            
            # Show in subplot
            axes[row, col].imshow(vis_image)
            
            # Set title
            num_detections = len(result.boxes)
            title = f"{img_path.name}\nDetected: {num_detections} objects"
            axes[row, col].set_title(title, fontsize=10)
            axes[row, col].axis('off')
        
        # Hide extra subplots
        for i in range(len(test_images), rows * cols):
            row = i // cols
            col = i % cols
            axes[row, col].axis('off')
        
        plt.tight_layout()
        
        # Save grid image
        if save_grid:
            output_dir = Path("evaluation_results")
            output_dir.mkdir(exist_ok=True)
            output_file = output_dir / "test_results_grid.png"
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            print(f"📁 Test results grid saved: {output_file}")
        
        # Print detection statistics
        total_samples = len(test_images)
        print(f"\n📊 Detection Statistics:")
        print(f"   Images with detections: {detection_stats['detected']}/{total_samples} ({detection_stats['detected']/total_samples*100:.1f}%)")
        print(f"   Images without detections: {detection_stats['not_detected']}/{total_samples} ({detection_stats['not_detected']/total_samples*100:.1f}%)")
        
        # Recommendations based on detection rate
        detection_rate = detection_stats['detected'] / total_samples
        if detection_rate < 0.5:
            print("\n💡 Low detection rate suggestions:")
            print("   • Try lowering confidence threshold (e.g., --conf 0.3)")
            print("   • Check if test images are similar to training data")
            print("   • Consider data augmentation or more training")
        
        plt.show()
    
    def batch_predict_testset(self, conf_threshold=0.5, save_results=True):
        """
        Batch predict test set
        
        Args:
            conf_threshold (float): Confidence threshold
            save_results (bool): Whether to save results
            
        Returns:
            list: Prediction results list
        """
        print(f"\n📦 Batch predicting test set (conf={conf_threshold})...")
        
        test_images_dir = self.dataset_path / "images" / "test"
        
        # Batch prediction
        results = self.model.predict(
            source=str(test_images_dir),
            conf=conf_threshold,
            save=save_results,
            verbose=False
        )
        
        print(f"✅ Completed prediction on {len(results)} images")
        
        # Statistics
        total_detections = sum(len(r.boxes) for r in results)
        images_with_detections = sum(1 for r in results if len(r.boxes) > 0)
        
        print(f"📊 Detection Statistics:")
        print(f"   Total detections: {total_detections}")
        print(f"   Images with detections: {images_with_detections}/{len(results)} ({images_with_detections/len(results)*100:.1f}%)")
        print(f"   Average detections per image: {total_detections/len(results):.2f}")
        
        # Confidence distribution
        if total_detections > 0:
            all_confidences = []
            for r in results:
                for box in r.boxes:
                    all_confidences.append(float(box.conf[0]))
            
            avg_conf = np.mean(all_confidences)
            min_conf = np.min(all_confidences)
            max_conf = np.max(all_confidences)
            
            print(f"   Confidence range: {min_conf:.3f} - {max_conf:.3f} (avg: {avg_conf:.3f})")
        
        return results

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="YOLOv13 Model Evaluation and Prediction")
    parser.add_argument("--model", type=str, 
                       default="runs/train/aqua_yolov13n_v1/weights/best.pt",
                       help="Model path")
    parser.add_argument("--dataset", type=str,
                       default="Datasets/Aqua_YOLO",
                       help="Dataset path")
    parser.add_argument("--image", type=str,
                       help="Single image prediction path")
    parser.add_argument("--conf", type=float, default=0.5,
                       help="Confidence threshold")
    parser.add_argument("--eval", action="store_true",
                       help="Evaluate test set performance")
    parser.add_argument("--visualize", action="store_true",
                       help="Visualize test results")
    parser.add_argument("--batch", action="store_true",
                       help="Batch predict test set")
    parser.add_argument("--samples", type=int, default=9,
                       help="Number of visualization samples")
    
    args = parser.parse_args()
    
    print("🛰️  YOLOv13 Model Evaluation and Prediction Tool")
    print("=" * 80)
    
    try:
        # Create evaluator
        evaluator = YOLOv13Evaluator(args.model, args.dataset)
        
        # Execute based on arguments
        if args.image:
            # Single image prediction
            evaluator.predict_single_image(args.image, conf_threshold=args.conf)
        
        elif args.eval:
            # Evaluate test set
            evaluator.evaluate_on_testset(conf_threshold=args.conf)
        
        elif args.visualize:
            # Visualize test results
            evaluator.visualize_test_results(num_samples=args.samples, conf_threshold=args.conf)
        
        elif args.batch:
            # Batch prediction
            evaluator.batch_predict_testset(conf_threshold=args.conf)
        
        else:
            # Default: run complete evaluation
            print("🚀 Running complete evaluation workflow...")
            
            # 1. Performance evaluation
            metrics = evaluator.evaluate_on_testset(conf_threshold=args.conf)
            
            # 2. Visualize results
            evaluator.visualize_test_results(num_samples=args.samples, conf_threshold=args.conf)
            
            # 3. Batch prediction statistics
            evaluator.batch_predict_testset(conf_threshold=args.conf)
            
            print("\n🎉 Evaluation completed!")
            print(f"📊 Key metrics: mAP50-95={metrics['mAP50-95']:.3f}, mAP50={metrics['mAP50']:.3f}")
            
            # Overall recommendations
            print("\n🎯 Overall Assessment:")
            if metrics['mAP50'] > 0.8 and metrics['precision'] > 0.8:
                print("✅ Excellent model performance! Ready for deployment.")
            elif metrics['mAP50'] > 0.6:
                print("✅ Good model performance. Consider fine-tuning for production use.")
            else:
                print("⚠️  Model performance needs improvement. Consider retraining.")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()