"""
Unified Training Interface for YOLO Models

This module provides a unified interface for training different YOLO model versions
with consistent configuration and logging.
"""

from typing import Dict, Any, Optional, List
import logging
from pathlib import Path
import time
import json

from models.registry import get_model, BaseModel
from utils.config import load_config, merge_configs
from utils.logging import setup_logging
from utils.device import get_device_info

logger = logging.getLogger(__name__)


class YOLOTrainer:
    """
    Unified trainer for YOLO models.
    
    This class provides a consistent interface for training different YOLO versions
    while handling configuration management, logging, and result tracking.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the trainer.
        
        Args:
            config: Training configuration
        """
        self.config = config
        self.model = None
        self.training_results = None
        self.start_time = None
        self.end_time = None
        
        # Setup logging
        setup_logging(config.get("logging", {}))
        
        # Log device information
        device_info = get_device_info()
        logger.info(f"Device info: {device_info}")
    
    def setup_model(self, model_config: Dict[str, Any]) -> BaseModel:
        """
        Setup the model for training.
        
        Args:
            model_config: Model configuration
            
        Returns:
            Initialized model
        """
        model_name = model_config.get("architecture", {}).get("name", "yolov13n")
        
        logger.info(f"Setting up model: {model_name}")
        
        # Get model from registry
        self.model = get_model(model_name, model_config)
        
        # Load model (with or without pretrained weights)
        training_config = model_config.get("training", {})
        if training_config.get("pretrained", True):
            self.model.load()
        else:
            # Load without pretrained weights
            yaml_path = model_config.get("architecture", {}).get("yaml_path")
            if yaml_path:
                self.model.load(yaml_path)
            else:
                self.model.load()
        
        logger.info(f"Model {model_name} loaded successfully")
        return self.model
    
    def prepare_training_args(self, training_config: Dict[str, Any], 
                            dataset_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare training arguments from configuration.
        
        Args:
            training_config: Training configuration
            dataset_config: Dataset configuration
            
        Returns:
            Training arguments dictionary
        """
        # Base training arguments
        train_args = {
            "data": dataset_config.get("path", ""),
            "epochs": training_config.get("epochs", 100),
            "batch": training_config.get("batch_size", 16),
            "imgsz": training_config.get("imgsz", 640),
            "device": training_config.get("device") or self.config.get("device", {}).get("type", "auto"),
            "workers": training_config.get("workers", 8),
            "project": training_config.get("project", "runs/train"),
            "name": training_config.get("name"),
            "exist_ok": training_config.get("exist_ok", False),
            "verbose": training_config.get("verbose", True),
            "seed": training_config.get("seed", 0),
            "deterministic": training_config.get("deterministic", True),
        }
        
        # Optimizer settings
        optimizer_config = training_config.get("optimizer", {})
        if optimizer_config:
            train_args.update({
                "optimizer": optimizer_config.get("name", "AdamW"),
                "lr0": optimizer_config.get("lr0", 0.001),
                "lrf": optimizer_config.get("lrf", 0.01),
                "momentum": optimizer_config.get("momentum", 0.937),
                "weight_decay": optimizer_config.get("weight_decay", 0.0005),
                "warmup_epochs": optimizer_config.get("warmup_epochs", 3),
                "warmup_momentum": optimizer_config.get("warmup_momentum", 0.8),
                "warmup_bias_lr": optimizer_config.get("warmup_bias_lr", 0.1),
            })
        
        # Loss function weights
        loss_config = training_config.get("loss", {})
        if loss_config:
            train_args.update({
                "box": loss_config.get("box", 7.5),
                "cls": loss_config.get("cls", 0.5),
                "dfl": loss_config.get("dfl", 1.5),
            })
        
        # Training settings
        train_settings = training_config.get("training", {})
        if train_settings:
            train_args.update({
                "amp": train_settings.get("amp", True),
                "cache": train_settings.get("cache", False),
                "single_cls": train_settings.get("single_cls", False),
                "rect": train_settings.get("rect", False),
                "cos_lr": train_settings.get("cos_lr", False),
                "close_mosaic": train_settings.get("close_mosaic", 10),
                "resume": train_settings.get("resume", False),
                "fraction": train_settings.get("fraction", 1.0),
            })
        
        # Validation settings
        val_config = training_config.get("validation", {})
        if val_config:
            train_args.update({
                "val": val_config.get("val", True),
                "split": val_config.get("split", "val"),
                "save_period": val_config.get("save_period", -1),
            })
        
        # Data augmentation (merge with dataset config)
        aug_config = training_config.get("augmentation", {})
        dataset_aug = dataset_config.get("augmentation", {})
        if aug_config or dataset_aug:
            # Dataset augmentation takes precedence
            merged_aug = {**aug_config, **dataset_aug}
            train_args.update(merged_aug)
        
        # Remove None values
        train_args = {k: v for k, v in train_args.items() if v is not None}
        
        return train_args
    
    def train(self, model_config: Dict[str, Any], 
              dataset_config: Dict[str, Any],
              training_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Train the model with given configurations.
        
        Args:
            model_config: Model configuration
            dataset_config: Dataset configuration
            training_config: Training configuration
            
        Returns:
            Training results
        """
        logger.info("Starting YOLO model training")
        logger.info(f"Model: {model_config.get('architecture', {}).get('name', 'unknown')}")
        logger.info(f"Dataset: {dataset_config.get('name', 'unknown')}")
        
        self.start_time = time.time()
        
        try:
            # Setup model
            self.setup_model(model_config)
            
            # Prepare training arguments
            train_args = self.prepare_training_args(training_config, dataset_config)
            
            logger.info(f"Training arguments: {train_args}")
            
            # Start training
            self.training_results = self.model.train(**train_args)
            
            self.end_time = time.time()
            training_time = self.end_time - self.start_time
            
            logger.info(f"Training completed successfully in {training_time:.2f} seconds")
            
            # Prepare results summary
            results_summary = self._prepare_results_summary(
                model_config, dataset_config, training_config, training_time
            )
            
            return results_summary
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise
    
    def _prepare_results_summary(self, model_config: Dict[str, Any],
                                dataset_config: Dict[str, Any],
                                training_config: Dict[str, Any],
                                training_time: float) -> Dict[str, Any]:
        """
        Prepare training results summary.
        
        Args:
            model_config: Model configuration
            dataset_config: Dataset configuration
            training_config: Training configuration
            training_time: Training time in seconds
            
        Returns:
            Results summary
        """
        summary = {
            "model": {
                "name": model_config.get("architecture", {}).get("name"),
                "variant": model_config.get("architecture", {}).get("variant"),
                "config": model_config
            },
            "dataset": {
                "name": dataset_config.get("name"),
                "path": dataset_config.get("path"),
                "classes": dataset_config.get("nc"),
                "config": dataset_config
            },
            "training": {
                "epochs": training_config.get("epochs"),
                "batch_size": training_config.get("batch_size"),
                "time_seconds": training_time,
                "config": training_config
            },
            "results": self.training_results,
            "timestamp": time.time(),
            "device_info": get_device_info()
        }
        
        return summary
    
    def save_results(self, results: Dict[str, Any], output_path: str) -> None:
        """
        Save training results to file.
        
        Args:
            results: Training results
            output_path: Output file path
        """
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Training results saved to: {output_file}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Model information
        """
        if self.model is None:
            return {"status": "no_model_loaded"}
        
        return self.model.get_model_info()


def train_model(config_path: str, overrides: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Convenience function to train a model from configuration file.
    
    Args:
        config_path: Path to configuration file
        overrides: Optional configuration overrides
        
    Returns:
        Training results
    """
    # Load configuration
    config = load_config(config_path)
    
    # Apply overrides
    if overrides:
        config = merge_configs(config, overrides)
    
    # Create trainer
    trainer = YOLOTrainer(config)
    
    # Extract configurations
    model_config = config.get("model", {})
    dataset_config = config.get("dataset", {})
    training_config = config.get("training", {})
    
    # Train model
    results = trainer.train(model_config, dataset_config, training_config)
    
    return results
