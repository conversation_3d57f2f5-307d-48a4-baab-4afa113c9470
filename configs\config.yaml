# Main configuration file for YOLO Baseline Platform
# This file defines the default configuration structure

defaults:
  - model: yolov13n
  - dataset: aqua
  - training: base
  - evaluation: standard
  - _self_

# Project settings
project:
  name: "yolo-baseline"
  version: "1.0.0"
  description: "YOLO Baseline Evaluation Platform"

# Paths configuration
paths:
  data_root: "Datasets"
  models_root: "models"
  experiments_root: "experiments"
  logs_root: "logs"

# Device configuration
device:
  type: "auto"  # auto, cpu, cuda, mps
  gpu_ids: [0]  # GPU IDs to use

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_logs: true

# Random seed for reproducibility
seed: 42

# Hydra configuration
hydra:
  run:
    dir: experiments/runs/${now:%Y-%m-%d_%H-%M-%S}
  sweep:
    dir: experiments/sweeps/${now:%Y-%m-%d_%H-%M-%S}
    subdir: ${hydra.job.num}
