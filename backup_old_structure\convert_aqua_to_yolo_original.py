#!/usr/bin/env python3
"""
Aqua Dataset to YOLO Format Converter

This script converts the Aqua satellite dataset from Unity Solo JSON format to YOLO format.
The dataset contains images of observation satellites with bounding box annotations.

Author: AI Assistant
Date: 2025-01-16
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import argparse
from tqdm import tqdm


class AquaToYOLOConverter:
    """Convert Aqua dataset from Unity Solo JSON format to YOLO format."""
    
    def __init__(self, source_dir: str, output_dir: str):
        """
        Initialize the converter.
        
        Args:
            source_dir: Path to the source Aqua dataset directory
            output_dir: Path to the output YOLO format directory
        """
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        
        # Class mapping - Aqua satellite is the only class
        self.class_mapping = {
            "Observation Satellite - Aqua": 0
        }
        
        # Image dimensions from the JSON files
        self.img_width = 1920
        self.img_height = 1080
        
    def create_output_structure(self):
        """Create YOLO dataset directory structure."""
        # Create main directories
        (self.output_dir / "images" / "train").mkdir(parents=True, exist_ok=True)
        (self.output_dir / "images" / "val").mkdir(parents=True, exist_ok=True)
        (self.output_dir / "images" / "test").mkdir(parents=True, exist_ok=True)
        (self.output_dir / "labels" / "train").mkdir(parents=True, exist_ok=True)
        (self.output_dir / "labels" / "val").mkdir(parents=True, exist_ok=True)
        (self.output_dir / "labels" / "test").mkdir(parents=True, exist_ok=True)

        print(f"Created output directory structure at: {self.output_dir}")
    
    def parse_json_annotation(self, json_path: Path) -> List[Dict]:
        """
        Parse Unity Solo JSON annotation file.
        
        Args:
            json_path: Path to the JSON annotation file
            
        Returns:
            List of bounding box annotations
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            annotations = []
            
            # Navigate through the JSON structure
            captures = data.get('captures', [])
            for capture in captures:
                if capture.get('@type') == 'type.unity.com/unity.solo.RGBCamera':
                    annotation_list = capture.get('annotations', [])
                    for annotation in annotation_list:
                        if annotation.get('@type') == 'type.unity.com/unity.solo.BoundingBox2DAnnotation':
                            values = annotation.get('values', [])
                            for value in values:
                                label_name = value.get('labelName', '')
                                if label_name in self.class_mapping:
                                    origin = value.get('origin', [0, 0])
                                    dimension = value.get('dimension', [0, 0])
                                    
                                    annotations.append({
                                        'class_name': label_name,
                                        'class_id': self.class_mapping[label_name],
                                        'x': origin[0],
                                        'y': origin[1],
                                        'width': dimension[0],
                                        'height': dimension[1]
                                    })
            
            return annotations
            
        except Exception as e:
            print(f"Error parsing {json_path}: {e}")
            return []
    
    def convert_to_yolo_format(self, annotations: List[Dict]) -> List[str]:
        """
        Convert bounding box annotations to YOLO format.
        
        Args:
            annotations: List of bounding box annotations
            
        Returns:
            List of YOLO format strings
        """
        yolo_lines = []
        
        for ann in annotations:
            # Unity Solo uses top-left origin, YOLO uses center
            x_center = (ann['x'] + ann['width'] / 2) / self.img_width
            y_center = (ann['y'] + ann['height'] / 2) / self.img_height
            width_norm = ann['width'] / self.img_width
            height_norm = ann['height'] / self.img_height
            
            # Ensure values are within [0, 1] range
            x_center = max(0, min(1, x_center))
            y_center = max(0, min(1, y_center))
            width_norm = max(0, min(1, width_norm))
            height_norm = max(0, min(1, height_norm))
            
            # YOLO format: class_id x_center y_center width height
            yolo_line = f"{ann['class_id']} {x_center:.6f} {y_center:.6f} {width_norm:.6f} {height_norm:.6f}"
            yolo_lines.append(yolo_line)
        
        return yolo_lines
    
    def split_dataset(self, file_list: List[Path], train_ratio: float = 0.7, val_ratio: float = 0.2) -> Tuple[List[Path], List[Path], List[Path]]:
        """
        Split dataset into train, validation and test sets.

        Args:
            file_list: List of file paths
            train_ratio: Ratio of training data (default: 0.7)
            val_ratio: Ratio of validation data (default: 0.2)

        Returns:
            Tuple of (train_files, val_files, test_files)
        """
        # Sort files to ensure consistent splitting
        file_list = sorted(file_list)

        total_files = len(file_list)
        train_split = int(total_files * train_ratio)
        val_split = int(total_files * (train_ratio + val_ratio))

        train_files = file_list[:train_split]
        val_files = file_list[train_split:val_split]
        test_files = file_list[val_split:]

        return train_files, val_files, test_files
    
    def process_files(self, json_files: List[Path], split: str):
        """
        Process a list of JSON files and convert them to YOLO format.
        
        Args:
            json_files: List of JSON annotation files
            split: Dataset split ('train' or 'val')
        """
        processed_count = 0
        skipped_count = 0
        
        for json_file in tqdm(json_files, desc=f"Processing {split} files"):
            # Get corresponding image file
            # JSON file: step0.frame_data.json -> Image file: step0.camera.png
            base_name = json_file.stem.replace('.frame_data', '')
            img_file = json_file.parent / f"{base_name}.camera.png"

            if not img_file.exists():
                print(f"Warning: Image file not found for {json_file}")
                print(f"Looking for: {img_file}")
                skipped_count += 1
                continue
            
            # Parse annotations
            annotations = self.parse_json_annotation(json_file)
            
            # Convert to YOLO format
            yolo_lines = self.convert_to_yolo_format(annotations)
            
            # Generate output filenames
            base_name = json_file.stem.replace('.frame_data', '')
            img_output = self.output_dir / "images" / split / f"{base_name}.png"
            label_output = self.output_dir / "labels" / split / f"{base_name}.txt"
            
            # Copy image file
            shutil.copy2(img_file, img_output)
            
            # Write YOLO annotation file
            with open(label_output, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_lines))
                if yolo_lines:  # Add newline at end if there are annotations
                    f.write('\n')
            
            processed_count += 1
        
        print(f"Processed {processed_count} {split} files, skipped {skipped_count} files")
    
    def create_dataset_yaml(self):
        """Create YOLO dataset configuration file."""
        yaml_content = f"""# Aqua Satellite Dataset Configuration
# Converted from Unity Solo format to YOLO format

# Dataset paths (relative to this file)
path: {self.output_dir.absolute()}
train: images/train
val: images/val
test: images/test

# Number of classes
nc: {len(self.class_mapping)}

# Class names
names:
"""

        # Add class names
        for class_name, class_id in self.class_mapping.items():
            yaml_content += f"  {class_id}: {class_name}\n"

        yaml_path = self.output_dir / "aqua_dataset.yaml"
        with open(yaml_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

        print(f"Created dataset configuration file: {yaml_path}")
    
    def convert(self, train_ratio: float = 0.7, val_ratio: float = 0.2):
        """
        Convert the entire Aqua dataset to YOLO format.

        Args:
            train_ratio: Ratio of training data (default: 0.7)
            val_ratio: Ratio of validation data (default: 0.2)
            Note: test_ratio = 1 - train_ratio - val_ratio (default: 0.1)
        """
        print("Starting Aqua to YOLO conversion...")
        print(f"Source directory: {self.source_dir}")
        print(f"Output directory: {self.output_dir}")

        # Create output directory structure
        self.create_output_structure()

        # Find all JSON annotation files
        json_files = list(self.source_dir.glob("*.frame_data.json"))

        if not json_files:
            raise ValueError(f"No JSON annotation files found in {self.source_dir}")

        print(f"Found {len(json_files)} annotation files")

        # Split dataset
        train_files, val_files, test_files = self.split_dataset(json_files, train_ratio, val_ratio)
        test_ratio = 1 - train_ratio - val_ratio
        print(f"Split: {len(train_files)} train ({train_ratio:.1f}), {len(val_files)} validation ({val_ratio:.1f}), {len(test_files)} test ({test_ratio:.1f})")

        # Process train, validation and test sets
        self.process_files(train_files, "train")
        self.process_files(val_files, "val")
        self.process_files(test_files, "test")

        # Create dataset configuration file
        self.create_dataset_yaml()

        print("Conversion completed successfully!")
        print(f"Dataset saved to: {self.output_dir}")


def run_simple_conversion():
    """Run the conversion with default settings without command line arguments."""

    # Default paths and ratios
    source_dir = "Datasets/Aqua/1-RGB-200张"
    output_dir = "Datasets/Aqua_YOLO"
    train_ratio = 0.7
    val_ratio = 0.2
    test_ratio = 0.1

    print("=" * 60)
    print("Aqua Dataset to YOLO Format Converter")
    print("=" * 60)
    print(f"Source directory: {source_dir}")
    print(f"Output directory: {output_dir}")
    print(f"Train/Val/Test split ratio: {train_ratio:.1f}/{val_ratio:.1f}/{test_ratio:.1f}")
    print("=" * 60)

    # Check if source directory exists
    if not Path(source_dir).exists():
        print(f"Error: Source directory '{source_dir}' does not exist!")
        print("Please make sure the Aqua dataset is in the correct location.")
        return False

    try:
        # Create converter and run conversion
        converter = AquaToYOLOConverter(source_dir, output_dir)
        converter.convert(train_ratio, val_ratio)

        print("\n" + "=" * 60)
        print("Conversion completed successfully!")
        print("=" * 60)
        print(f"YOLO dataset created at: {Path(output_dir).absolute()}")
        print("\nDataset structure:")
        print("├── images/")
        print("│   ├── train/")
        print("│   ├── val/")
        print("│   └── test/")
        print("├── labels/")
        print("│   ├── train/")
        print("│   ├── val/")
        print("│   └── test/")
        print("└── aqua_dataset.yaml")
        print("\nYou can now use this dataset with YOLOv13:")
        print("python train.py --data Datasets/Aqua_YOLO/aqua_dataset.yaml")

        return True

    except Exception as e:
        print(f"\nError during conversion: {e}")
        return False


def main():
    """Main function to run the converter."""
    parser = argparse.ArgumentParser(description="Convert Aqua dataset to YOLO format")
    parser.add_argument(
        "--source",
        type=str,
        default="Datasets/Aqua/1-RGB-200张",
        help="Path to source Aqua dataset directory"
    )
    parser.add_argument(
        "--output",
        type=str,
        default="Datasets/Aqua_YOLO",
        help="Path to output YOLO dataset directory"
    )
    parser.add_argument(
        "--train-ratio",
        type=float,
        default=0.7,
        help="Ratio of training data (default: 0.7)"
    )
    parser.add_argument(
        "--val-ratio",
        type=float,
        default=0.2,
        help="Ratio of validation data (default: 0.2)"
    )
    parser.add_argument(
        "--simple",
        action="store_true",
        help="Run with default settings without arguments"
    )

    args = parser.parse_args()

    if args.simple:
        # Run simple conversion with default settings
        success = run_simple_conversion()
        exit(0 if success else 1)
    else:
        # Create converter and run conversion with provided arguments
        converter = AquaToYOLOConverter(args.source, args.output)
        converter.convert(args.train_ratio, args.val_ratio)


if __name__ == "__main__":
    main()
