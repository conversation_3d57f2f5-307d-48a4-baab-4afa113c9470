#!/usr/bin/env python3
"""
YOLO Dataset Visualization Tool

This script visualizes YOLO format datasets to verify the correctness of annotations.
It displays images with bounding boxes overlaid and shows class information.

Author: AI Assistant
Date: 2025-01-16
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import yaml
import argparse
import random
from typing import List, Tuple, Dict, Optional


class YOLODatasetVisualizer:
    """Visualize YOLO format dataset with bounding boxes."""
    
    def __init__(self, dataset_path: str):
        """
        Initialize the visualizer.
        
        Args:
            dataset_path: Path to the YOLO dataset directory
        """
        self.dataset_path = Path(dataset_path)
        self.class_names = {}
        self.colors = {}
        
        # Load dataset configuration
        self.load_dataset_config()
        
        # Generate colors for each class
        self.generate_colors()
    
    def load_dataset_config(self):
        """Load dataset configuration from YAML file."""
        yaml_files = list(self.dataset_path.glob("*.yaml"))
        if not yaml_files:
            print("Warning: No YAML configuration file found. Using default class names.")
            self.class_names = {0: "Unknown"}
            return
        
        yaml_file = yaml_files[0]
        try:
            with open(yaml_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            self.class_names = config.get('names', {0: "Unknown"})
            print(f"Loaded dataset config from: {yaml_file}")
            print(f"Classes: {self.class_names}")
            
        except Exception as e:
            print(f"Error loading YAML config: {e}")
            self.class_names = {0: "Unknown"}
    
    def generate_colors(self):
        """Generate distinct colors for each class."""
        np.random.seed(42)  # For consistent colors
        for class_id in self.class_names.keys():
            # Generate bright, distinct colors
            color = np.random.randint(0, 255, 3)
            self.colors[class_id] = tuple(map(int, color))
    
    def parse_yolo_annotation(self, label_file: Path, img_width: int, img_height: int) -> List[Dict]:
        """
        Parse YOLO format annotation file.
        
        Args:
            label_file: Path to the label file
            img_width: Image width in pixels
            img_height: Image height in pixels
            
        Returns:
            List of bounding box annotations
        """
        annotations = []
        
        if not label_file.exists():
            return annotations
        
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) != 5:
                    continue
                
                class_id = int(parts[0])
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                
                # Convert YOLO format to pixel coordinates
                x_center_px = x_center * img_width
                y_center_px = y_center * img_height
                width_px = width * img_width
                height_px = height * img_height
                
                # Calculate top-left corner
                x1 = int(x_center_px - width_px / 2)
                y1 = int(y_center_px - height_px / 2)
                x2 = int(x_center_px + width_px / 2)
                y2 = int(y_center_px + height_px / 2)
                
                annotations.append({
                    'class_id': class_id,
                    'class_name': self.class_names.get(class_id, f"Class_{class_id}"),
                    'bbox': (x1, y1, x2, y2),
                    'confidence': 1.0  # Ground truth has confidence 1.0
                })
        
        except Exception as e:
            print(f"Error parsing {label_file}: {e}")
        
        return annotations
    
    def draw_bounding_boxes(self, image: np.ndarray, annotations: List[Dict]) -> np.ndarray:
        """
        Draw bounding boxes on image.
        
        Args:
            image: Input image (BGR format)
            annotations: List of annotations
            
        Returns:
            Image with bounding boxes drawn
        """
        img_with_boxes = image.copy()
        
        for ann in annotations:
            class_id = ann['class_id']
            class_name = ann['class_name']
            x1, y1, x2, y2 = ann['bbox']
            color = self.colors.get(class_id, (0, 255, 0))
            
            # Draw bounding box
            cv2.rectangle(img_with_boxes, (x1, y1), (x2, y2), color, 2)
            
            # Draw class label
            label = f"{class_name}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # Draw label background
            cv2.rectangle(img_with_boxes, 
                         (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), 
                         color, -1)
            
            # Draw label text
            cv2.putText(img_with_boxes, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return img_with_boxes
    
    def visualize_sample(self, split: str = "train", sample_idx: Optional[int] = None, 
                        save_path: Optional[str] = None) -> bool:
        """
        Visualize a single sample from the dataset.
        
        Args:
            split: Dataset split ('train', 'val', or 'test')
            sample_idx: Index of sample to visualize (random if None)
            save_path: Path to save the visualization (optional)
            
        Returns:
            True if successful, False otherwise
        """
        # Get image and label directories
        img_dir = self.dataset_path / "images" / split
        label_dir = self.dataset_path / "labels" / split
        
        if not img_dir.exists():
            print(f"Error: Image directory not found: {img_dir}")
            return False
        
        # Get all image files
        img_files = list(img_dir.glob("*.png")) + list(img_dir.glob("*.jpg")) + list(img_dir.glob("*.jpeg"))
        
        if not img_files:
            print(f"Error: No image files found in {img_dir}")
            return False
        
        # Select sample
        if sample_idx is None:
            sample_idx = random.randint(0, len(img_files) - 1)
        elif sample_idx >= len(img_files):
            print(f"Error: Sample index {sample_idx} out of range (0-{len(img_files)-1})")
            return False
        
        img_file = img_files[sample_idx]
        label_file = label_dir / f"{img_file.stem}.txt"
        
        # Load image
        image = cv2.imread(str(img_file))
        if image is None:
            print(f"Error: Could not load image {img_file}")
            return False
        
        img_height, img_width = image.shape[:2]
        
        # Parse annotations
        annotations = self.parse_yolo_annotation(label_file, img_width, img_height)
        
        # Draw bounding boxes
        img_with_boxes = self.draw_bounding_boxes(image, annotations)
        
        # Convert BGR to RGB for matplotlib
        img_rgb = cv2.cvtColor(img_with_boxes, cv2.COLOR_BGR2RGB)
        
        # Create visualization
        plt.figure(figsize=(12, 8))
        plt.imshow(img_rgb)
        plt.title(f"Dataset: {self.dataset_path.name} | Split: {split} | Sample: {sample_idx}\n"
                 f"File: {img_file.name} | Annotations: {len(annotations)}")
        plt.axis('off')
        
        # Add legend
        if annotations:
            legend_elements = []
            unique_classes = set(ann['class_id'] for ann in annotations)
            for class_id in unique_classes:
                color = [c/255.0 for c in self.colors[class_id]]  # Normalize for matplotlib
                class_name = self.class_names.get(class_id, f"Class_{class_id}")
                legend_elements.append(patches.Patch(color=color, label=class_name))
            
            plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1, 1))
        
        plt.tight_layout()
        
        # Save or show
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"Visualization saved to: {save_path}")
        else:
            plt.show()
        
        # Print annotation details
        print(f"\nImage: {img_file.name}")
        print(f"Size: {img_width}x{img_height}")
        print(f"Annotations: {len(annotations)}")
        for i, ann in enumerate(annotations):
            x1, y1, x2, y2 = ann['bbox']
            print(f"  {i+1}. {ann['class_name']}: ({x1}, {y1}) -> ({x2}, {y2})")
        
        return True
    
    def visualize_multiple(self, split: str = "train", num_samples: int = 4, 
                          save_path: Optional[str] = None):
        """
        Visualize multiple samples in a grid.
        
        Args:
            split: Dataset split ('train', 'val', or 'test')
            num_samples: Number of samples to visualize
            save_path: Path to save the visualization (optional)
        """
        # Get image and label directories
        img_dir = self.dataset_path / "images" / split
        label_dir = self.dataset_path / "labels" / split
        
        if not img_dir.exists():
            print(f"Error: Image directory not found: {img_dir}")
            return
        
        # Get all image files
        img_files = list(img_dir.glob("*.png")) + list(img_dir.glob("*.jpg")) + list(img_dir.glob("*.jpeg"))
        
        if not img_files:
            print(f"Error: No image files found in {img_dir}")
            return
        
        # Select random samples
        sample_indices = random.sample(range(len(img_files)), min(num_samples, len(img_files)))
        
        # Calculate grid size
        cols = min(2, num_samples)
        rows = (num_samples + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
        if rows == 1 and cols == 1:
            axes = [axes]
        elif rows == 1 or cols == 1:
            axes = axes.flatten()
        else:
            axes = axes.flatten()
        
        for i, sample_idx in enumerate(sample_indices):
            img_file = img_files[sample_idx]
            label_file = label_dir / f"{img_file.stem}.txt"
            
            # Load image
            image = cv2.imread(str(img_file))
            if image is None:
                continue
            
            img_height, img_width = image.shape[:2]
            
            # Parse annotations
            annotations = self.parse_yolo_annotation(label_file, img_width, img_height)
            
            # Draw bounding boxes
            img_with_boxes = self.draw_bounding_boxes(image, annotations)
            
            # Convert BGR to RGB for matplotlib
            img_rgb = cv2.cvtColor(img_with_boxes, cv2.COLOR_BGR2RGB)
            
            # Plot
            ax = axes[i] if len(sample_indices) > 1 else axes[0]
            ax.imshow(img_rgb)
            ax.set_title(f"{img_file.name}\n{len(annotations)} annotations")
            ax.axis('off')
        
        # Hide unused subplots
        for i in range(len(sample_indices), len(axes)):
            axes[i].axis('off')
        
        plt.suptitle(f"Dataset: {self.dataset_path.name} | Split: {split}")
        plt.tight_layout()
        
        # Save or show
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"Visualization saved to: {save_path}")
        else:
            plt.show()
    
    def get_dataset_stats(self):
        """Print dataset statistics."""
        print(f"\n{'='*60}")
        print(f"Dataset Statistics: {self.dataset_path.name}")
        print(f"{'='*60}")
        
        for split in ['train', 'val', 'test']:
            img_dir = self.dataset_path / "images" / split
            label_dir = self.dataset_path / "labels" / split
            
            if not img_dir.exists():
                continue
            
            img_files = list(img_dir.glob("*.png")) + list(img_dir.glob("*.jpg")) + list(img_dir.glob("*.jpeg"))
            label_files = list(label_dir.glob("*.txt"))
            
            total_annotations = 0
            class_counts = {}
            
            for label_file in label_files:
                try:
                    with open(label_file, 'r') as f:
                        lines = f.readlines()
                    
                    for line in lines:
                        line = line.strip()
                        if line:
                            class_id = int(line.split()[0])
                            class_counts[class_id] = class_counts.get(class_id, 0) + 1
                            total_annotations += 1
                except:
                    continue
            
            print(f"\n{split.upper()} Split:")
            print(f"  Images: {len(img_files)}")
            print(f"  Labels: {len(label_files)}")
            print(f"  Total annotations: {total_annotations}")
            
            if class_counts:
                print(f"  Class distribution:")
                for class_id, count in sorted(class_counts.items()):
                    class_name = self.class_names.get(class_id, f"Class_{class_id}")
                    print(f"    {class_name}: {count}")


def main():
    """Main function to run the visualizer."""
    parser = argparse.ArgumentParser(description="Visualize YOLO dataset")
    parser.add_argument(
        "--dataset", 
        type=str, 
        default="Datasets/Aqua_YOLO",
        help="Path to YOLO dataset directory"
    )
    parser.add_argument(
        "--split", 
        type=str, 
        default="train",
        choices=["train", "val", "test"],
        help="Dataset split to visualize"
    )
    parser.add_argument(
        "--sample", 
        type=int, 
        default=None,
        help="Specific sample index to visualize (random if not specified)"
    )
    parser.add_argument(
        "--multiple", 
        type=int, 
        default=1,
        help="Number of samples to visualize in grid"
    )
    parser.add_argument(
        "--save", 
        type=str, 
        default=None,
        help="Path to save visualization image"
    )
    parser.add_argument(
        "--stats", 
        action="store_true",
        help="Show dataset statistics"
    )
    
    args = parser.parse_args()
    
    # Create visualizer
    visualizer = YOLODatasetVisualizer(args.dataset)
    
    # Show statistics if requested
    if args.stats:
        visualizer.get_dataset_stats()
    
    # Visualize samples
    if args.multiple > 1:
        visualizer.visualize_multiple(args.split, args.multiple, args.save)
    else:
        visualizer.visualize_sample(args.split, args.sample, args.save)


def quick_visualize():
    """Quick visualization with default settings."""
    print("=" * 60)
    print("YOLO Dataset Visualization Tool")
    print("=" * 60)

    dataset_path = "Datasets/Aqua_YOLO"

    # Check if dataset exists
    if not Path(dataset_path).exists():
        print(f"Error: Dataset not found at {dataset_path}")
        print("Please run the conversion script first.")
        return

    # Create visualizer
    visualizer = YOLODatasetVisualizer(dataset_path)

    # Show dataset statistics
    visualizer.get_dataset_stats()

    print(f"\n{'='*60}")
    print("Visualizing samples...")
    print("=" * 60)

    # Visualize samples from each split
    for split in ['train', 'val', 'test']:
        img_dir = Path(dataset_path) / "images" / split
        if img_dir.exists() and list(img_dir.glob("*.png")):
            print(f"\nShowing random sample from {split} split:")
            visualizer.visualize_sample(split)

            # Ask user if they want to continue
            response = input(f"\nShow more samples from {split}? (y/n): ").lower()
            if response == 'y':
                visualizer.visualize_multiple(split, 4)


if __name__ == "__main__":
    import sys

    # If no arguments provided, run quick visualization
    if len(sys.argv) == 1:
        quick_visualize()
    else:
        main()
