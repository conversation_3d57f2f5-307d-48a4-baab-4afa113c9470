# YOLO Baseline Platform Requirements

# Core ML/DL frameworks
torch>=2.0.0
torchvision>=0.15.0
ultralytics>=8.0.0

# Configuration management
hydra-core>=1.3.0
omegaconf>=2.3.0
pyyaml>=6.0

# Data processing
numpy>=1.21.0
pandas>=1.5.0
opencv-python>=4.8.0
pillow>=9.0.0

# Visualization and plotting
matplotlib>=3.6.0
seaborn>=0.12.0
plotly>=5.15.0

# Metrics and evaluation
scikit-learn>=1.3.0
scipy>=1.10.0

# Utilities
tqdm>=4.64.0
psutil>=5.9.0
pathlib2>=2.3.0

# Logging and monitoring
tensorboard>=2.13.0
wandb>=0.15.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0

# Optional: GPU acceleration
# nvidia-ml-py3>=7.352.0  # For GPU monitoring

# Optional: Advanced optimizations
# onnx>=1.14.0
# onnxruntime>=1.15.0
# tensorrt>=8.6.0  # NVIDIA TensorRT (requires separate installation)

# Optional: Cloud storage
# boto3>=1.26.0  # AWS S3
# google-cloud-storage>=2.10.0  # Google Cloud Storage
