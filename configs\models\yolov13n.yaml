# YOLOv13-N Model Configuration
# Nano version with HyperACE and FullPAD innovations

_target_: models.yolov13.YOLOv13

# Model architecture
architecture:
  name: "yolov13n"
  variant: "nano"
  yaml_path: "ultralytics/cfg/models/v13/yolov13.yaml"

# Model specifications
specs:
  input_size: [640, 640]
  num_classes: 1  # Will be overridden by dataset config
  channels: 3
  
# Performance characteristics
performance:
  params_m: 2.5      # Parameters in millions
  flops_g: 6.4       # FLOPs in billions
  latency_ms: 1.97   # Inference latency
  
# HyperACE configuration
hyperace:
  enabled: true
  num_hyperedges: 8
  context: "both"    # "local", "global", "both"
  channel_adjust: true

# FullPAD configuration
fullpad:
  enabled: true
  tunnel_gates: true
  distribution_channels: 3

# Lightweight modules
lightweight:
  use_dsc: true      # Use depthwise separable convolutions
  use_dsc3k2: true   # Use DSC3k2 blocks
  use_a2c2f: true    # Use A2C2f modules

# Training specific settings
training:
  pretrained: true
  freeze_backbone: false
  gradient_checkpointing: false

# Export settings
export:
  formats: ["pt", "onnx", "engine"]
  optimize: true
  half: true
