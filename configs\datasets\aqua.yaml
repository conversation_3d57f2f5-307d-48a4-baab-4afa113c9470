# Aqua Satellite Dataset Configuration
# Unity Solo format converted to YOLO format

# Dataset information
name: "aqua"
description: "Aqua observation satellite detection dataset"
task: "detect"
format: "yolo"

# Paths (relative to data_root)
path: "Aqua_YOLO/aqua_dataset.yaml"
train: "images/train"
val: "images/val"
test: "images/test"

# Class information
nc: 1  # Number of classes
names:
  0: "Observation Satellite - Aqua"

# Dataset statistics
stats:
  total_images: 200
  train_images: 140  # 70%
  val_images: 39     # 20%
  test_images: 20    # 10%

# Image specifications
image:
  width: 1920
  height: 1080
  channels: 3
  format: "jpg"

# Original dataset information
source:
  format: "unity_solo"
  path: "Aqua"
  annotation_file: "step*.frame_data.json"

# Conversion settings
conversion:
  converter: "aqua_to_yolo"
  split_ratios:
    train: 0.7
    val: 0.2
    test: 0.1
  random_seed: 42

# Data augmentation settings
augmentation:
  enabled: true
  hsv_h: 0.015
  hsv_s: 0.7
  hsv_v: 0.4
  degrees: 0.0
  translate: 0.1
  scale: 0.5
  shear: 0.0
  perspective: 0.0
  flipud: 0.0
  fliplr: 0.5
  mosaic: 1.0
  mixup: 0.0
  copy_paste: 0.0

# Validation settings
validation:
  conf_threshold: 0.25
  iou_threshold: 0.7
  max_det: 300
