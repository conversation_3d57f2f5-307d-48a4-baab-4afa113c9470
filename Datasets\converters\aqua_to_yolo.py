"""
Aqua Dataset to YOLO Format Converter

This module converts Unity Solo format Aqua satellite dataset to YOLO format.
"""

import json
import shutil
from pathlib import Path
from typing import List, Dict, Any
import logging

from .base_converter import BaseConverter, ImageAnnotation, BoundingBox

logger = logging.getLogger(__name__)


class AquaToYOLOConverter(BaseConverter):
    """
    Converter for Aqua satellite dataset from Unity Solo format to YOLO format.
    
    Unity Solo format uses JSON files with bounding box annotations.
    YOLO format uses text files with normalized coordinates.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Aqua to YOLO converter.
        
        Args:
            config: Converter configuration
        """
        super().__init__(config)
        self.source_format = "unity_solo"
        self.target_format = "yolo"
        
        # Default class names for Aqua dataset
        if not self.class_names:
            self.class_names = ["Observation Satellite - Aqua"]
    
    def load_annotations(self, source_path: str) -> List[ImageAnnotation]:
        """
        Load annotations from Unity Solo JSON files.
        
        Args:
            source_path: Path to source dataset directory
            
        Returns:
            List of image annotations
        """
        source_dir = Path(source_path)
        annotations = []
        
        logger.info(f"Loading annotations from: {source_dir}")
        
        # Find all JSON annotation files
        json_files = list(source_dir.glob("*.frame_data.json"))
        
        if not json_files:
            logger.warning(f"No JSON annotation files found in {source_dir}")
            return annotations
        
        for json_file in json_files:
            try:
                annotation = self._load_single_annotation(json_file)
                if annotation:
                    annotations.append(annotation)
            except Exception as e:
                logger.error(f"Failed to load annotation from {json_file}: {e}")
        
        logger.info(f"Loaded {len(annotations)} annotations from {len(json_files)} files")
        return annotations
    
    def _load_single_annotation(self, json_file: Path) -> ImageAnnotation:
        """
        Load annotation from a single JSON file.
        
        Args:
            json_file: Path to JSON annotation file
            
        Returns:
            Image annotation or None if failed
        """
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        # Extract image information
        captures = data.get("captures", [])
        if not captures:
            logger.warning(f"No captures found in {json_file}")
            return None
        
        capture = captures[0]  # Assume single capture per file
        
        # Get image filename (replace .json with .png/.jpg)
        image_name = json_file.stem.replace(".frame_data", ".png")
        image_path = json_file.parent / image_name
        
        # Check if image exists, try different extensions
        if not image_path.exists():
            image_path = json_file.parent / (json_file.stem.replace(".frame_data", ".jpg"))
        
        if not image_path.exists():
            logger.warning(f"Image file not found for {json_file}")
            return None
        
        # Get image dimensions
        image_width = capture.get("dimension", {}).get("x", 1920)
        image_height = capture.get("dimension", {}).get("y", 1080)
        
        # Extract bounding boxes
        bboxes = []
        annotations_data = capture.get("annotations", [])
        
        for ann in annotations_data:
            if ann.get("@type") == "type.unity.com/unity.solo.BoundingBox2DAnnotation":
                values = ann.get("values", [])
                
                for value in values:
                    # Extract bounding box coordinates
                    origin = value.get("origin", [0, 0])
                    dimension = value.get("dimension", [0, 0])
                    
                    # Convert to normalized YOLO format
                    x_center = (origin[0] + dimension[0] / 2) / image_width
                    y_center = (origin[1] + dimension[1] / 2) / image_height
                    width = dimension[0] / image_width
                    height = dimension[1] / image_height
                    
                    # Get class ID (default to 0 for Aqua satellite)
                    label_name = value.get("labelName", "Observation Satellite - Aqua")
                    class_id = self._get_class_id(label_name)
                    
                    bbox = BoundingBox(
                        x=x_center,
                        y=y_center,
                        width=width,
                        height=height,
                        class_id=class_id
                    )
                    bboxes.append(bbox)
        
        return ImageAnnotation(
            image_path=str(image_path),
            image_width=image_width,
            image_height=image_height,
            bboxes=bboxes,
            metadata={"source_file": str(json_file)}
        )
    
    def _get_class_id(self, label_name: str) -> int:
        """
        Get class ID for label name.
        
        Args:
            label_name: Label name
            
        Returns:
            Class ID
        """
        if label_name in self.class_names:
            return self.class_names.index(label_name)
        
        # Default to class 0 for Aqua dataset
        return 0
    
    def save_annotations(self, annotations: List[ImageAnnotation], 
                        output_path: str) -> None:
        """
        Save annotations in YOLO format.
        
        Args:
            annotations: List of image annotations
            output_path: Output directory path
        """
        output_dir = Path(output_path)
        
        # Create directory structure
        images_dir = output_dir / "images"
        labels_dir = output_dir / "labels"
        images_dir.mkdir(parents=True, exist_ok=True)
        labels_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Saving {len(annotations)} annotations to: {output_dir}")
        
        for annotation in annotations:
            try:
                self._save_single_annotation(annotation, images_dir, labels_dir)
            except Exception as e:
                logger.error(f"Failed to save annotation for {annotation.image_path}: {e}")
    
    def _save_single_annotation(self, annotation: ImageAnnotation, 
                               images_dir: Path, labels_dir: Path) -> None:
        """
        Save a single annotation in YOLO format.
        
        Args:
            annotation: Image annotation
            images_dir: Images directory
            labels_dir: Labels directory
        """
        # Copy image file
        source_image = Path(annotation.image_path)
        target_image = images_dir / source_image.name
        
        if source_image.exists():
            shutil.copy2(source_image, target_image)
        else:
            logger.warning(f"Source image not found: {source_image}")
        
        # Save label file
        label_file = labels_dir / (source_image.stem + ".txt")
        
        with open(label_file, 'w') as f:
            for bbox in annotation.bboxes:
                # YOLO format: class_id x_center y_center width height
                line = f"{bbox.class_id} {bbox.x:.6f} {bbox.y:.6f} {bbox.width:.6f} {bbox.height:.6f}\n"
                f.write(line)
    
    def get_conversion_summary(self) -> Dict[str, Any]:
        """
        Get summary of Aqua to YOLO conversion.
        
        Returns:
            Conversion summary
        """
        summary = super().get_conversion_summary()
        summary.update({
            "source_format_details": {
                "format": "Unity Solo JSON",
                "annotation_type": "BoundingBox2DAnnotation",
                "coordinate_system": "absolute pixels"
            },
            "target_format_details": {
                "format": "YOLO text files",
                "coordinate_system": "normalized (0-1)",
                "format_spec": "class_id x_center y_center width height"
            }
        })
        return summary
