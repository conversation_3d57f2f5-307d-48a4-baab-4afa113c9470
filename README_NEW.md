# YOLO Baseline Evaluation Platform

A comprehensive platform for evaluating and comparing different YOLO model versions on various datasets.

## 🎯 Project Overview

This platform provides a unified framework for:
- Training and evaluating multiple YOLO versions (YOLOv8, YOLOv11, YOLOv12, YOLOv13, etc.)
- Managing datasets and format conversions
- Comparing model performance across different metrics
- Conducting systematic experiments with reproducible results

## 🏗️ Project Structure

```
yolo-baseline/
├── configs/                    # Configuration files
│   ├── models/                # Model configurations
│   │   ├── yolov8/           # YOLOv8 variants
│   │   ├── yolov11/          # YOLOv11 variants
│   │   ├── yolov12/          # YOLOv12 variants
│   │   └── yolov13/          # YOLOv13 variants
│   ├── datasets/             # Dataset configurations
│   │   ├── aqua.yaml         # Aqua satellite dataset
│   │   ├── coco.yaml         # COCO dataset
│   │   └── custom.yaml       # Custom dataset template
│   ├── training/             # Training configurations
│   │   ├── base.yaml         # Base training config
│   │   ├── fast.yaml         # Fast training config
│   │   └── full.yaml         # Full training config
│   ├── evaluation/           # Evaluation configurations
│   └── experiments/          # Experiment configurations
├── datasets/                  # Dataset management
│   ├── converters/           # Format conversion tools
│   │   ├── aqua_to_yolo.py   # Aqua to YOLO converter
│   │   ├── coco_to_yolo.py   # COCO to YOLO converter
│   │   └── base_converter.py # Base converter class
│   ├── loaders/              # Data loading utilities
│   └── visualizers/          # Dataset visualization tools
├── models/                    # Model definitions and registry
│   ├── registry.py           # Model registration system
│   ├── yolov8/              # YOLOv8 implementations
│   ├── yolov11/             # YOLOv11 implementations
│   ├── yolov12/             # YOLOv12 implementations
│   ├── yolov13/             # YOLOv13 implementations
│   └── base_model.py        # Base model interface
├── training/                  # Training modules
│   ├── trainer.py           # Unified trainer
│   ├── callbacks.py         # Training callbacks
│   └── schedulers.py        # Learning rate schedulers
├── evaluation/               # Evaluation modules
│   ├── evaluator.py         # Model evaluator
│   ├── metrics.py           # Evaluation metrics
│   ├── comparator.py        # Model comparison
│   └── visualizer.py        # Results visualization
├── utils/                    # Utility functions
│   ├── config.py            # Configuration utilities
│   ├── logging.py           # Logging setup
│   ├── paths.py             # Path management
│   └── device.py            # Device management
├── experiments/              # Experiment results
│   ├── runs/                # Training runs
│   ├── evaluations/         # Evaluation results
│   └── comparisons/         # Model comparisons
├── scripts/                  # Convenience scripts
│   ├── train.py             # Training script
│   ├── evaluate.py          # Evaluation script
│   ├── compare.py           # Comparison script
│   └── convert_dataset.py   # Dataset conversion script
├── tests/                    # Unit tests
├── docs/                     # Documentation
└── requirements.txt          # Dependencies
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd yolo-baseline

# Install dependencies
pip install -r requirements.txt
```

### 2. Dataset Preparation

```bash
# Convert Aqua dataset to YOLO format
python scripts/convert_dataset.py dataset=aqua

# Visualize dataset
python scripts/visualize_dataset.py dataset=aqua
```

### 3. Model Training

```bash
# Train YOLOv13-N on Aqua dataset
python scripts/train.py model=yolov13n dataset=aqua training=base

# Train with custom configuration
python scripts/train.py model=yolov11s dataset=aqua training=fast epochs=100
```

### 4. Model Evaluation

```bash
# Evaluate single model
python scripts/evaluate.py model=yolov13n dataset=aqua weights=path/to/weights.pt

# Compare multiple models
python scripts/compare.py models=[yolov8n,yolov11n,yolov13n] dataset=aqua
```

## 📊 Features

### Model Support
- **YOLOv8**: All variants (n, s, m, l, x)
- **YOLOv11**: All variants with latest improvements
- **YOLOv12**: Next-generation architecture
- **YOLOv13**: HyperACE and FullPAD innovations

### Dataset Support
- **Aqua Satellite**: Unity Solo format to YOLO conversion
- **COCO**: Standard object detection dataset
- **Custom**: Easy integration of new datasets

### Evaluation Metrics
- **Detection**: mAP@0.5, mAP@0.5:0.95, Precision, Recall
- **Performance**: FPS, Latency, Memory usage
- **Model Size**: Parameters, FLOPs, Model file size

### Experiment Management
- **Configuration-driven**: Hydra-based configuration system
- **Reproducible**: Automatic seed setting and environment tracking
- **Comparative**: Side-by-side model comparison
- **Visualization**: Rich plots and charts for analysis

## 🔧 Configuration System

The platform uses Hydra for configuration management, allowing for:
- Hierarchical configuration composition
- Command-line overrides
- Experiment tracking
- Configuration validation

Example configuration override:
```bash
python scripts/train.py model=yolov13n dataset=aqua training.epochs=200 training.batch_size=32
```

## 📈 Experiment Tracking

All experiments are automatically tracked with:
- Configuration snapshots
- Training metrics and logs
- Model checkpoints
- Evaluation results
- Comparison reports

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Ultralytics for the YOLO implementations
- Facebook Research for Hydra configuration framework
- The computer vision community for continuous innovations
