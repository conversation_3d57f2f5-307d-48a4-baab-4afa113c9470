task: detect
mode: train
model: ultralytics/cfg/models/v13/yolov13.yaml
data: Datasets/Aqua_YOLO/aqua_dataset.yaml
epochs: 150
time: null
patience: 40
batch: 8
imgsz: 640
save: true
save_period: 15
cache: false
device: auto
workers: 0
project: null
name: train3
exist_ok: false
pretrained: true
optimizer: auto
verbose: true
seed: 42
deterministic: true
single_cls: false
rect: false
cos_lr: true
close_mosaic: 15
resume: false
amp: true
fraction: 1.0
profile: false
freeze: null
multi_scale: true
overlap_mask: true
mask_ratio: 4
dropout: 0.0
val: true
split: val
save_json: false
save_hybrid: false
conf: 0.25
iou: 0.6
max_det: 100
half: false
dnn: false
plots: true
source: null
vid_stride: 1
stream_buffer: false
visualize: false
augment: false
agnostic_nms: false
classes: null
retina_masks: false
embed: null
show: false
save_frames: false
save_txt: false
save_conf: false
save_crop: false
show_labels: true
show_conf: true
show_boxes: true
line_width: null
format: torchscript
keras: false
optimize: false
int8: false
dynamic: false
simplify: true
opset: null
workspace: null
nms: false
lr0: 0.005
lrf: 0.05
momentum: 0.9
weight_decay: 0.0001
warmup_epochs: 5
warmup_momentum: 0.8
warmup_bias_lr: 0.1
box: 7.5
cls: 1.0
dfl: 1.5
pose: 12.0
kobj: 1.0
nbs: 64
hsv_h: 0.02
hsv_s: 0.8
hsv_v: 0.6
degrees: 15.0
translate: 0.1
scale: 0.3
shear: 5.0
perspective: 0.0001
flipud: 0.0
fliplr: 0.5
bgr: 0.0
mosaic: 0.8
mixup: 0.1
copy_paste: 0.0
copy_paste_mode: flip
auto_augment: randaugment
erasing: 0.4
crop_fraction: 1.0
cfg: null
tracker: botsort.yaml
save_dir: C:\Users\<USER>\Desktop\yolov13\runs\detect\train3
