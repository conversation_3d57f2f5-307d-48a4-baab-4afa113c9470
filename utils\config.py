"""
Configuration Management Utilities

This module provides utilities for loading, merging, and validating configurations
using Hydra and YAML files.
"""

from typing import Dict, Any, Optional, Union
import yaml
from pathlib import Path
import logging
from omegaconf import OmegaConf, DictConfig
import hydra
from hydra.core.config_store import ConfigStore
from hydra.core.global_hydra import GlobalHydra

logger = logging.getLogger(__name__)


def load_yaml_config(config_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Load configuration from YAML file.
    
    Args:
        config_path: Path to YAML configuration file
        
    Returns:
        Configuration dictionary
    """
    config_file = Path(config_path)
    
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        logger.info(f"Loaded configuration from: {config_path}")
        return config
        
    except yaml.YAMLError as e:
        logger.error(f"Failed to parse YAML file {config_path}: {e}")
        raise
    except Exception as e:
        logger.error(f"Failed to load configuration from {config_path}: {e}")
        raise


def save_yaml_config(config: Dict[str, Any], output_path: Union[str, Path]) -> None:
    """
    Save configuration to YAML file.
    
    Args:
        config: Configuration dictionary
        output_path: Output file path
    """
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        logger.info(f"Saved configuration to: {output_path}")
        
    except Exception as e:
        logger.error(f"Failed to save configuration to {output_path}: {e}")
        raise


def load_config(config_path: Union[str, Path]) -> DictConfig:
    """
    Load configuration using OmegaConf.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        OmegaConf configuration
    """
    config_file = Path(config_path)
    
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    try:
        config = OmegaConf.load(config_file)
        logger.info(f"Loaded OmegaConf configuration from: {config_path}")
        return config
        
    except Exception as e:
        logger.error(f"Failed to load OmegaConf configuration from {config_path}: {e}")
        raise


def merge_configs(*configs: Union[Dict[str, Any], DictConfig]) -> DictConfig:
    """
    Merge multiple configurations with later configs taking precedence.
    
    Args:
        *configs: Configuration dictionaries or DictConfigs to merge
        
    Returns:
        Merged configuration
    """
    if not configs:
        return OmegaConf.create({})
    
    # Convert all configs to OmegaConf
    omega_configs = []
    for config in configs:
        if isinstance(config, DictConfig):
            omega_configs.append(config)
        else:
            omega_configs.append(OmegaConf.create(config))
    
    # Merge configurations
    merged = omega_configs[0]
    for config in omega_configs[1:]:
        merged = OmegaConf.merge(merged, config)
    
    return merged


def resolve_config_path(config_name: str, config_dir: str = "configs") -> Path:
    """
    Resolve configuration file path.
    
    Args:
        config_name: Configuration name (with or without .yaml extension)
        config_dir: Configuration directory
        
    Returns:
        Resolved configuration path
    """
    config_dir_path = Path(config_dir)
    
    # Add .yaml extension if not present
    if not config_name.endswith('.yaml'):
        config_name += '.yaml'
    
    # Try direct path first
    config_path = config_dir_path / config_name
    if config_path.exists():
        return config_path
    
    # Try searching in subdirectories
    for subdir in config_dir_path.iterdir():
        if subdir.is_dir():
            potential_path = subdir / config_name
            if potential_path.exists():
                return potential_path
    
    raise FileNotFoundError(f"Configuration file '{config_name}' not found in '{config_dir}'")


def validate_config(config: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate configuration against a schema.
    
    Args:
        config: Configuration to validate
        schema: Validation schema
        
    Returns:
        Validation report
    """
    report = {
        "valid": True,
        "errors": [],
        "warnings": []
    }
    
    def validate_recursive(cfg: Dict[str, Any], sch: Dict[str, Any], path: str = ""):
        for key, expected_type in sch.items():
            current_path = f"{path}.{key}" if path else key
            
            if key not in cfg:
                if isinstance(expected_type, dict) and expected_type.get("required", False):
                    report["errors"].append(f"Missing required field: {current_path}")
                    report["valid"] = False
                continue
            
            value = cfg[key]
            
            if isinstance(expected_type, dict):
                if "type" in expected_type:
                    expected_type_class = expected_type["type"]
                    if not isinstance(value, expected_type_class):
                        report["errors"].append(
                            f"Type mismatch at {current_path}: expected {expected_type_class.__name__}, "
                            f"got {type(value).__name__}"
                        )
                        report["valid"] = False
                
                if "schema" in expected_type and isinstance(value, dict):
                    validate_recursive(value, expected_type["schema"], current_path)
            
            elif isinstance(expected_type, type):
                if not isinstance(value, expected_type):
                    report["errors"].append(
                        f"Type mismatch at {current_path}: expected {expected_type.__name__}, "
                        f"got {type(value).__name__}"
                    )
                    report["valid"] = False
    
    try:
        validate_recursive(config, schema)
    except Exception as e:
        report["errors"].append(f"Validation error: {e}")
        report["valid"] = False
    
    return report


def get_config_template(config_type: str) -> Dict[str, Any]:
    """
    Get configuration template for a specific type.
    
    Args:
        config_type: Type of configuration (model, dataset, training, etc.)
        
    Returns:
        Configuration template
    """
    templates = {
        "model": {
            "architecture": {
                "name": "yolov13n",
                "variant": "nano",
                "yaml_path": "ultralytics/cfg/models/v13/yolov13.yaml"
            },
            "specs": {
                "input_size": [640, 640],
                "num_classes": 1,
                "channels": 3
            },
            "training": {
                "pretrained": True,
                "freeze_backbone": False
            }
        },
        
        "dataset": {
            "name": "custom_dataset",
            "description": "Custom dataset description",
            "task": "detect",
            "format": "yolo",
            "path": "path/to/dataset",
            "train": "images/train",
            "val": "images/val",
            "test": "images/test",
            "nc": 1,
            "names": {0: "class_name"}
        },
        
        "training": {
            "epochs": 100,
            "batch_size": 16,
            "imgsz": 640,
            "optimizer": {
                "name": "AdamW",
                "lr0": 0.001,
                "weight_decay": 0.0005
            },
            "validation": {
                "val": True,
                "split": "val"
            }
        },
        
        "evaluation": {
            "task": "detect",
            "split": "test",
            "imgsz": 640,
            "thresholds": {
                "conf": 0.25,
                "iou": 0.7
            },
            "metrics": ["mAP@0.5", "mAP@0.5:0.95", "precision", "recall"]
        }
    }
    
    if config_type not in templates:
        raise ValueError(f"Unknown configuration type: {config_type}. "
                        f"Available types: {list(templates.keys())}")
    
    return templates[config_type].copy()


def create_experiment_config(model_name: str, dataset_name: str, 
                           experiment_name: str,
                           overrides: Optional[Dict[str, Any]] = None) -> DictConfig:
    """
    Create experiment configuration by combining model, dataset, and training configs.
    
    Args:
        model_name: Model configuration name
        dataset_name: Dataset configuration name
        experiment_name: Experiment name
        overrides: Optional configuration overrides
        
    Returns:
        Experiment configuration
    """
    # Load base configurations
    try:
        model_config = load_config(resolve_config_path(model_name, "configs/models"))
        dataset_config = load_config(resolve_config_path(dataset_name, "configs/datasets"))
        training_config = load_config(resolve_config_path("base", "configs/training"))
        
    except FileNotFoundError as e:
        logger.error(f"Failed to load configuration: {e}")
        raise
    
    # Create experiment configuration
    experiment_config = OmegaConf.create({
        "experiment": {
            "name": experiment_name,
            "model": model_name,
            "dataset": dataset_name
        },
        "model": model_config,
        "dataset": dataset_config,
        "training": training_config
    })
    
    # Apply overrides
    if overrides:
        experiment_config = merge_configs(experiment_config, overrides)
    
    return experiment_config
