"""
YOLOv13 Model Implementation

This module provides YOLOv13 model implementations with HyperACE and FullPAD innovations.
"""

from .model import YOLOv13
from ..registry import register_model

# Register YOLOv13 variants
@register_model("yolov13n", aliases=["yolo13n", "v13n"])
class YOLOv13N(YOLOv13):
    """YOLOv13 Nano variant."""
    pass

@register_model("yolov13s", aliases=["yolo13s", "v13s"])
class YOLOv13S(YOLOv13):
    """YOLOv13 Small variant."""
    pass

@register_model("yolov13m", aliases=["yolo13m", "v13m"])
class YOLOv13M(YOLOv13):
    """YOLOv13 Medium variant."""
    pass

@register_model("yolov13l", aliases=["yolo13l", "v13l"])
class YOLOv13L(YOLOv13):
    """YOLOv13 Large variant."""
    pass

@register_model("yolov13x", aliases=["yolo13x", "v13x"])
class YOLOv13X(YOLOv13):
    """YOLOv13 Extra Large variant."""
    pass

__all__ = ["YOLOv13", "YOLOv13N", "YOLOv13S", "YOLOv13M", "YOLOv13L", "YOLOv13X"]
