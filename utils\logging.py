"""
Logging Utilities

This module provides logging setup and utilities for the YOLO baseline platform.
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional
import sys
from datetime import datetime


def setup_logging(config: Optional[Dict[str, Any]] = None) -> None:
    """
    Setup logging configuration.
    
    Args:
        config: Logging configuration dictionary
    """
    if config is None:
        config = {}
    
    # Default configuration
    level = config.get("level", "INFO")
    format_str = config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    save_logs = config.get("save_logs", True)
    log_dir = config.get("log_dir", "logs")
    max_bytes = config.get("max_bytes", 10 * 1024 * 1024)  # 10MB
    backup_count = config.get("backup_count", 5)
    
    # Create log directory
    if save_logs:
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(format_str)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if enabled)
    if save_logs:
        # Main log file
        log_file = log_path / "yolo_baseline.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=max_bytes, backupCount=backup_count
        )
        file_handler.setLevel(getattr(logging, level.upper()))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # Error log file
        error_log_file = log_path / "errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=max_bytes, backupCount=backup_count
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
    
    # Set specific logger levels
    logger_levels = config.get("logger_levels", {})
    for logger_name, logger_level in logger_levels.items():
        specific_logger = logging.getLogger(logger_name)
        specific_logger.setLevel(getattr(logging, logger_level.upper()))
    
    logging.info("Logging setup completed")


def get_experiment_logger(experiment_name: str, log_dir: str = "experiments") -> logging.Logger:
    """
    Get a logger for a specific experiment.
    
    Args:
        experiment_name: Name of the experiment
        log_dir: Base log directory
        
    Returns:
        Configured logger for the experiment
    """
    # Create experiment log directory
    exp_log_dir = Path(log_dir) / experiment_name / "logs"
    exp_log_dir.mkdir(parents=True, exist_ok=True)
    
    # Create experiment logger
    logger_name = f"experiment.{experiment_name}"
    exp_logger = logging.getLogger(logger_name)
    
    # Avoid duplicate handlers
    if exp_logger.handlers:
        return exp_logger
    
    exp_logger.setLevel(logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Experiment log file
    exp_log_file = exp_log_dir / f"{experiment_name}.log"
    exp_handler = logging.handlers.RotatingFileHandler(
        exp_log_file, maxBytes=10*1024*1024, backupCount=3
    )
    exp_handler.setLevel(logging.INFO)
    exp_handler.setFormatter(formatter)
    exp_logger.addHandler(exp_handler)
    
    return exp_logger


class TrainingLogger:
    """
    Logger for training progress and metrics.
    """
    
    def __init__(self, experiment_name: str, log_dir: str = "experiments"):
        """
        Initialize training logger.
        
        Args:
            experiment_name: Name of the experiment
            log_dir: Base log directory
        """
        self.experiment_name = experiment_name
        self.log_dir = Path(log_dir) / experiment_name / "logs"
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = get_experiment_logger(experiment_name, log_dir)
        self.metrics_file = self.log_dir / "metrics.log"
        self.progress_file = self.log_dir / "progress.log"
        
    def log_training_start(self, config: Dict[str, Any]) -> None:
        """
        Log training start information.
        
        Args:
            config: Training configuration
        """
        self.logger.info(f"Starting training for experiment: {self.experiment_name}")
        self.logger.info(f"Configuration: {config}")
        
        # Log to progress file
        with open(self.progress_file, 'a') as f:
            f.write(f"\n{'='*50}\n")
            f.write(f"Training started at: {datetime.now()}\n")
            f.write(f"Experiment: {self.experiment_name}\n")
            f.write(f"{'='*50}\n")
    
    def log_epoch_metrics(self, epoch: int, metrics: Dict[str, float]) -> None:
        """
        Log metrics for an epoch.
        
        Args:
            epoch: Epoch number
            metrics: Metrics dictionary
        """
        # Log to main logger
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.logger.info(f"Epoch {epoch}: {metrics_str}")
        
        # Log to metrics file
        with open(self.metrics_file, 'a') as f:
            timestamp = datetime.now().isoformat()
            f.write(f"{timestamp},{epoch}")
            for metric_name, metric_value in metrics.items():
                f.write(f",{metric_value}")
            f.write("\n")
    
    def log_training_end(self, results: Dict[str, Any]) -> None:
        """
        Log training completion information.
        
        Args:
            results: Training results
        """
        self.logger.info(f"Training completed for experiment: {self.experiment_name}")
        self.logger.info(f"Results: {results}")
        
        # Log to progress file
        with open(self.progress_file, 'a') as f:
            f.write(f"Training completed at: {datetime.now()}\n")
            f.write(f"Results: {results}\n")
            f.write(f"{'='*50}\n")
    
    def log_error(self, error: Exception) -> None:
        """
        Log training error.
        
        Args:
            error: Exception that occurred
        """
        self.logger.error(f"Training error in experiment {self.experiment_name}: {error}")
        
        # Log to progress file
        with open(self.progress_file, 'a') as f:
            f.write(f"Training failed at: {datetime.now()}\n")
            f.write(f"Error: {error}\n")
            f.write(f"{'='*50}\n")


class EvaluationLogger:
    """
    Logger for evaluation progress and results.
    """
    
    def __init__(self, experiment_name: str, log_dir: str = "experiments"):
        """
        Initialize evaluation logger.
        
        Args:
            experiment_name: Name of the experiment
            log_dir: Base log directory
        """
        self.experiment_name = experiment_name
        self.log_dir = Path(log_dir) / experiment_name / "logs"
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = get_experiment_logger(experiment_name, log_dir)
        self.results_file = self.log_dir / "evaluation_results.log"
    
    def log_evaluation_start(self, config: Dict[str, Any]) -> None:
        """
        Log evaluation start information.
        
        Args:
            config: Evaluation configuration
        """
        self.logger.info(f"Starting evaluation for experiment: {self.experiment_name}")
        self.logger.info(f"Configuration: {config}")
    
    def log_evaluation_results(self, results: Dict[str, Any]) -> None:
        """
        Log evaluation results.
        
        Args:
            results: Evaluation results
        """
        self.logger.info(f"Evaluation results for {self.experiment_name}: {results}")
        
        # Log to results file
        with open(self.results_file, 'a') as f:
            timestamp = datetime.now().isoformat()
            f.write(f"{timestamp}: {results}\n")
    
    def log_evaluation_error(self, error: Exception) -> None:
        """
        Log evaluation error.
        
        Args:
            error: Exception that occurred
        """
        self.logger.error(f"Evaluation error in experiment {self.experiment_name}: {error}")


def configure_ultralytics_logging(level: str = "INFO") -> None:
    """
    Configure Ultralytics logging to integrate with our logging system.
    
    Args:
        level: Logging level for Ultralytics
    """
    # Configure Ultralytics logger
    ultralytics_logger = logging.getLogger("ultralytics")
    ultralytics_logger.setLevel(getattr(logging, level.upper()))
    
    # Prevent duplicate logs
    ultralytics_logger.propagate = True


def log_system_info() -> None:
    """Log system information for debugging purposes."""
    import platform
    import psutil
    import torch
    
    logger = logging.getLogger(__name__)
    
    logger.info("System Information:")
    logger.info(f"Platform: {platform.platform()}")
    logger.info(f"Python: {platform.python_version()}")
    logger.info(f"CPU: {platform.processor()}")
    logger.info(f"Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB")
    
    if torch.cuda.is_available():
        logger.info(f"CUDA: {torch.version.cuda}")
        logger.info(f"GPU: {torch.cuda.get_device_name()}")
        logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f} GB")
    else:
        logger.info("CUDA: Not available")
