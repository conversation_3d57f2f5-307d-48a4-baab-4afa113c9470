#!/usr/bin/env python3
"""
Dataset Conversion Script for YOLO Baseline Platform

This script converts datasets from various formats to YOLO format
using the converter registry system.

Usage:
    python scripts/convert_dataset.py dataset=aqua
    python scripts/convert_dataset.py dataset=aqua converter.split_ratios.train=0.8
    python scripts/convert_dataset.py --config-path=configs --config-name=config dataset=aqua
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import hydra
from omegaconf import DictConfig, OmegaConf
import logging
from typing import Dict, Any

from datasets.converters.aqua_to_yolo import AquaToYOLOConverter
from utils.logging import setup_logging
from utils.config import load_config

logger = logging.getLogger(__name__)


# Converter registry
CONVERTERS = {
    "aqua_to_yolo": AquaToYOLOConverter,
    "unity_solo_to_yolo": AquaToYOLOConverter,  # <PERSON>as
}


def get_converter(converter_name: str, config: Dict[str, Any]):
    """
    Get converter instance by name.
    
    Args:
        converter_name: Name of the converter
        config: Converter configuration
        
    Returns:
        Converter instance
    """
    if converter_name not in CONVERTERS:
        available = list(CONVERTERS.keys())
        raise ValueError(f"Converter '{converter_name}' not found. Available: {available}")
    
    converter_class = CONVERTERS[converter_name]
    return converter_class(config)


@hydra.main(version_base=None, config_path="../configs", config_name="config")
def convert_dataset(cfg: DictConfig) -> None:
    """
    Main dataset conversion function with Hydra configuration.
    
    Args:
        cfg: Hydra configuration
    """
    try:
        # Setup logging
        setup_logging(OmegaConf.to_container(cfg.get("logging", {})))
        
        # Log configuration
        logger.info("Dataset Conversion Configuration:")
        logger.info(OmegaConf.to_yaml(cfg.dataset))
        
        # Get dataset configuration
        dataset_config = OmegaConf.to_container(cfg.dataset)
        
        # Get conversion settings
        conversion_config = dataset_config.get("conversion", {})
        converter_name = conversion_config.get("converter", "aqua_to_yolo")
        
        # Prepare converter configuration
        converter_config = {
            "source_format": dataset_config.get("source", {}).get("format", "unity_solo"),
            "target_format": "yolo",
            "class_names": [dataset_config["names"][i] for i in sorted(dataset_config["names"].keys())],
            "random_seed": conversion_config.get("random_seed", 42),
            **conversion_config
        }
        
        # Get paths
        data_root = Path(cfg.paths.data_root)
        source_path = data_root / dataset_config.get("source", {}).get("path", dataset_config["name"])
        output_path = data_root / dataset_config["path"]
        
        # Validate source path
        if not source_path.exists():
            logger.error(f"Source dataset path not found: {source_path}")
            logger.info("Please ensure the source dataset is available.")
            return
        
        logger.info(f"Converting dataset from: {source_path}")
        logger.info(f"Output path: {output_path}")
        logger.info(f"Using converter: {converter_name}")
        
        # Get converter
        converter = get_converter(converter_name, converter_config)
        
        # Get split ratios
        split_ratios = conversion_config.get("split_ratios")
        
        # Convert dataset
        logger.info("Starting dataset conversion...")
        stats = converter.convert(
            source_path=str(source_path),
            output_path=str(output_path),
            split_ratios=split_ratios
        )
        
        # Log conversion results
        logger.info("Dataset conversion completed successfully!")
        logger.info(f"Conversion statistics: {stats}")
        
        # Validate converted dataset
        logger.info("Validating converted dataset...")
        annotations = converter.load_annotations(str(source_path))
        validation_report = converter.validate_annotations(annotations)
        
        logger.info(f"Validation report: {validation_report}")
        
        if validation_report["issues"]:
            logger.warning(f"Found {len(validation_report['issues'])} validation issues:")
            for issue in validation_report["issues"][:10]:  # Show first 10 issues
                logger.warning(f"  - {issue}")
            if len(validation_report["issues"]) > 10:
                logger.warning(f"  ... and {len(validation_report['issues']) - 10} more issues")
        
        # Log conversion summary
        summary = converter.get_conversion_summary()
        logger.info("Conversion Summary:")
        for key, value in summary.items():
            logger.info(f"  {key}: {value}")
        
        logger.info(f"Dataset ready for training at: {output_path}")
        
    except Exception as e:
        logger.error(f"Dataset conversion failed: {e}")
        raise


def main():
    """Main entry point for standalone execution."""
    try:
        convert_dataset()
    except Exception as e:
        logger.error(f"Dataset conversion script failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
