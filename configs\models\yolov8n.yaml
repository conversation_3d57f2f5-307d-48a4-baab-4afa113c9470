# YOLOv8-N Model Configuration
# Proven architecture with excellent performance

_target_: models.yolov8.YOLOv8

# Model architecture
architecture:
  name: "yolov8n"
  variant: "nano"
  yaml_path: "ultralytics/cfg/models/v8/yolov8n.yaml"

# Model specifications
specs:
  input_size: [640, 640]
  num_classes: 1  # Will be overridden by dataset config
  channels: 3
  
# Performance characteristics
performance:
  params_m: 3.2      # Parameters in millions
  flops_g: 8.7       # FLOPs in billions
  latency_ms: 2.0    # Inference latency
  
# Architecture features
features:
  c2f_blocks: true
  sppf: true
  pan_fpn: true
  anchor_free: true

# Training specific settings
training:
  pretrained: true
  freeze_backbone: false
  gradient_checkpointing: false

# Export settings
export:
  formats: ["pt", "onnx", "engine"]
  optimize: true
  half: true
