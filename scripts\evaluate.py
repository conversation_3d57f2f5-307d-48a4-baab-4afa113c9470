#!/usr/bin/env python3
"""
Evaluation Script for YOLO Baseline Platform

This script provides a command-line interface for evaluating YOLO models
with Hydra configuration management.

Usage:
    python scripts/evaluate.py model=yolov13n dataset=aqua weights=path/to/weights.pt
    python scripts/evaluate.py model=yolov11s dataset=aqua weights=runs/train/exp/weights/best.pt
    python scripts/evaluate.py --config-path=configs --config-name=config model=yolov13n evaluation.split=test
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import hydra
from omegaconf import DictConfig, OmegaConf
import logging
from typing import Optional, Dict, Any
import json
import time

from models.registry import get_model, list_available_models
from utils.logging import setup_logging, EvaluationLogger, log_system_info
from utils.device import get_device_info, get_optimal_device

logger = logging.getLogger(__name__)


class YOLOEvaluator:
    """
    YOLO model evaluator with comprehensive metrics and reporting.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize evaluator.
        
        Args:
            config: Evaluation configuration
        """
        self.config = config
        self.model = None
        self.results = None
        
    def setup_model(self, model_config: Dict[str, Any], weights_path: str) -> None:
        """
        Setup model for evaluation.
        
        Args:
            model_config: Model configuration
            weights_path: Path to model weights
        """
        model_name = model_config.get("architecture", {}).get("name", "yolov13n")
        
        logger.info(f"Setting up model: {model_name}")
        logger.info(f"Loading weights from: {weights_path}")
        
        # Validate weights file
        weights_file = Path(weights_path)
        if not weights_file.exists():
            raise FileNotFoundError(f"Weights file not found: {weights_path}")
        
        # Get model from registry
        self.model = get_model(model_name, model_config)
        
        # Load model with weights
        self.model.load(str(weights_file))
        
        logger.info(f"Model {model_name} loaded successfully")
    
    def prepare_evaluation_args(self, eval_config: Dict[str, Any], 
                               dataset_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare evaluation arguments.
        
        Args:
            eval_config: Evaluation configuration
            dataset_config: Dataset configuration
            
        Returns:
            Evaluation arguments
        """
        # Base evaluation arguments
        eval_args = {
            "data": dataset_config.get("path", ""),
            "split": eval_config.get("split", "test"),
            "imgsz": eval_config.get("imgsz", 640),
            "batch": eval_config.get("settings", {}).get("batch_size", 32),
            "device": eval_config.get("settings", {}).get("device") or get_optimal_device(),
            "workers": eval_config.get("settings", {}).get("workers", 8),
            "verbose": eval_config.get("settings", {}).get("verbose", True),
        }
        
        # Detection thresholds
        thresholds = eval_config.get("thresholds", {})
        if thresholds:
            eval_args.update({
                "conf": thresholds.get("conf", 0.25),
                "iou": thresholds.get("iou", 0.7),
                "max_det": thresholds.get("max_det", 300),
            })
        
        # Advanced settings
        advanced = eval_config.get("advanced", {})
        if advanced:
            eval_args.update({
                "augment": advanced.get("augment", False),
                "agnostic_nms": advanced.get("agnostic_nms", False),
                "single_cls": advanced.get("single_cls", False),
                "half": advanced.get("half", False),
                "dnn": advanced.get("dnn", False),
            })
        
        # Output settings
        output = eval_config.get("output", {})
        if output:
            eval_args.update({
                "save_txt": output.get("save_predictions", True),
                "save_conf": True,
                "save_json": True,
            })
        
        # Visualization settings
        visualization = eval_config.get("visualization", {})
        if visualization:
            eval_args.update({
                "plots": visualization.get("save_plots", True),
            })
        
        # Remove None values
        eval_args = {k: v for k, v in eval_args.items() if v is not None}
        
        return eval_args
    
    def evaluate(self, model_config: Dict[str, Any], 
                dataset_config: Dict[str, Any],
                eval_config: Dict[str, Any],
                weights_path: str) -> Dict[str, Any]:
        """
        Evaluate the model.
        
        Args:
            model_config: Model configuration
            dataset_config: Dataset configuration
            eval_config: Evaluation configuration
            weights_path: Path to model weights
            
        Returns:
            Evaluation results
        """
        logger.info("Starting model evaluation")
        logger.info(f"Model: {model_config.get('architecture', {}).get('name', 'unknown')}")
        logger.info(f"Dataset: {dataset_config.get('name', 'unknown')}")
        logger.info(f"Weights: {weights_path}")
        
        start_time = time.time()
        
        try:
            # Setup model
            self.setup_model(model_config, weights_path)
            
            # Prepare evaluation arguments
            eval_args = self.prepare_evaluation_args(eval_config, dataset_config)
            
            logger.info(f"Evaluation arguments: {eval_args}")
            
            # Run evaluation
            self.results = self.model.val(**eval_args)
            
            end_time = time.time()
            evaluation_time = end_time - start_time
            
            logger.info(f"Evaluation completed in {evaluation_time:.2f} seconds")
            
            # Prepare results summary
            results_summary = self._prepare_results_summary(
                model_config, dataset_config, eval_config, weights_path, evaluation_time
            )
            
            return results_summary
            
        except Exception as e:
            logger.error(f"Evaluation failed: {e}")
            raise
    
    def _prepare_results_summary(self, model_config: Dict[str, Any],
                                dataset_config: Dict[str, Any],
                                eval_config: Dict[str, Any],
                                weights_path: str,
                                evaluation_time: float) -> Dict[str, Any]:
        """
        Prepare evaluation results summary.
        
        Args:
            model_config: Model configuration
            dataset_config: Dataset configuration
            eval_config: Evaluation configuration
            weights_path: Path to weights
            evaluation_time: Evaluation time in seconds
            
        Returns:
            Results summary
        """
        # Extract metrics from results
        metrics = {}
        if self.results and hasattr(self.results, 'results_dict'):
            results_dict = self.results.results_dict
            
            # Standard detection metrics
            metrics.update({
                "mAP50": results_dict.get("metrics/mAP50(B)", 0.0),
                "mAP50-95": results_dict.get("metrics/mAP50-95(B)", 0.0),
                "precision": results_dict.get("metrics/precision(B)", 0.0),
                "recall": results_dict.get("metrics/recall(B)", 0.0),
            })
            
            # Calculate F1 score
            precision = metrics.get("precision", 0.0)
            recall = metrics.get("recall", 0.0)
            if precision + recall > 0:
                metrics["f1"] = 2 * (precision * recall) / (precision + recall)
            else:
                metrics["f1"] = 0.0
        
        summary = {
            "model": {
                "name": model_config.get("architecture", {}).get("name"),
                "variant": model_config.get("architecture", {}).get("variant"),
                "weights_path": weights_path,
                "config": model_config
            },
            "dataset": {
                "name": dataset_config.get("name"),
                "path": dataset_config.get("path"),
                "split": eval_config.get("split", "test"),
                "classes": dataset_config.get("nc"),
                "config": dataset_config
            },
            "evaluation": {
                "time_seconds": evaluation_time,
                "config": eval_config
            },
            "metrics": metrics,
            "results": self.results,
            "timestamp": time.time(),
            "device_info": get_device_info()
        }
        
        return summary
    
    def save_results(self, results: Dict[str, Any], output_path: str) -> None:
        """
        Save evaluation results to file.
        
        Args:
            results: Evaluation results
            output_path: Output file path
        """
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create a JSON-serializable version
        json_results = {}
        for key, value in results.items():
            if key == "results":
                # Skip the raw results object for JSON serialization
                continue
            else:
                json_results[key] = value
        
        with open(output_file, 'w') as f:
            json.dump(json_results, f, indent=2, default=str)
        
        logger.info(f"Evaluation results saved to: {output_file}")


@hydra.main(version_base=None, config_path="../configs", config_name="config")
def evaluate_model(cfg: DictConfig) -> Optional[float]:
    """
    Main evaluation function with Hydra configuration.
    
    Args:
        cfg: Hydra configuration
        
    Returns:
        mAP50-95 score if available
    """
    try:
        # Setup logging
        setup_logging(OmegaConf.to_container(cfg.get("logging", {})))
        
        # Log system information
        log_system_info()
        
        # Check for weights parameter
        weights_path = cfg.get("weights")
        if not weights_path:
            logger.error("Weights path not specified. Use: weights=path/to/weights.pt")
            return None
        
        # Log configuration
        logger.info("Evaluation Configuration:")
        logger.info(OmegaConf.to_yaml(cfg))
        
        # Validate model availability
        model_name = cfg.model.architecture.name
        available_models = list_available_models()
        
        if model_name not in available_models:
            logger.error(f"Model '{model_name}' not available. Available models: {available_models}")
            return None
        
        # Create experiment name
        experiment_name = f"eval_{model_name}_{cfg.dataset.name}_{int(time.time())}"
        
        # Setup experiment logger
        exp_logger = EvaluationLogger(experiment_name)
        exp_logger.log_evaluation_start(OmegaConf.to_container(cfg))
        
        # Create evaluator
        evaluator = YOLOEvaluator(OmegaConf.to_container(cfg))
        
        # Extract configurations
        model_config = OmegaConf.to_container(cfg.model)
        dataset_config = OmegaConf.to_container(cfg.dataset)
        eval_config = OmegaConf.to_container(cfg.evaluation)
        
        # Validate dataset path
        dataset_path = Path(cfg.paths.data_root) / dataset_config["path"]
        if not dataset_path.exists():
            logger.error(f"Dataset path not found: {dataset_path}")
            return None
        
        # Update dataset path to absolute path
        dataset_config["path"] = str(dataset_path / "dataset.yaml")
        
        # Evaluate model
        logger.info("Starting model evaluation...")
        results = evaluator.evaluate(model_config, dataset_config, eval_config, weights_path)
        
        # Log evaluation completion
        exp_logger.log_evaluation_results(results)
        
        # Save results
        results_dir = Path(cfg.paths.experiments_root) / "evaluations"
        results_file = results_dir / f"{experiment_name}_results.json"
        evaluator.save_results(results, results_file)
        
        # Extract mAP for return
        map_score = results.get("metrics", {}).get("mAP50-95", 0.0)
        
        logger.info(f"Evaluation completed successfully. mAP50-95: {map_score:.4f}")
        return map_score
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        if 'exp_logger' in locals():
            exp_logger.log_evaluation_error(e)
        raise


def main():
    """Main entry point for standalone execution."""
    try:
        evaluate_model()
    except Exception as e:
        logger.error(f"Evaluation script failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
