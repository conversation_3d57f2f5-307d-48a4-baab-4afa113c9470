#!/usr/bin/env python3
"""
Test configuration loading
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import hydra
from omegaconf import DictConfig, OmegaConf

@hydra.main(version_base=None, config_path="configs", config_name="config")
def test_config(cfg: DictConfig) -> None:
    """Test configuration loading."""
    print("Configuration loaded successfully!")
    print("\nModel config:")
    print(OmegaConf.to_yaml(cfg.model))
    print("\nDataset config:")
    print(OmegaConf.to_yaml(cfg.dataset))
    print("\nTraining config:")
    print(OmegaConf.to_yaml(cfg.training))
    
    # Check dataset path
    dataset_path = Path(cfg.paths.data_root) / cfg.dataset.path
    print(f"\nDataset path: {dataset_path}")
    print(f"Dataset exists: {dataset_path.exists()}")

if __name__ == "__main__":
    test_config()
