"""
Base Dataset Converter

This module provides the base class for dataset format converters.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import logging
import json
import yaml
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class BoundingBox:
    """Bounding box representation."""
    x: float
    y: float
    width: float
    height: float
    class_id: int
    confidence: float = 1.0


@dataclass
class ImageAnnotation:
    """Image annotation representation."""
    image_path: str
    image_width: int
    image_height: int
    bboxes: List[BoundingBox]
    metadata: Optional[Dict[str, Any]] = None


class BaseConverter(ABC):
    """
    Base class for dataset format converters.
    
    This class provides a common interface for converting between different
    dataset formats (e.g., Unity Solo to YOLO, COCO to YOLO, etc.).
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the converter.
        
        Args:
            config: Converter configuration
        """
        self.config = config
        self.source_format = config.get("source_format", "unknown")
        self.target_format = config.get("target_format", "yolo")
        self.class_names = config.get("class_names", [])
        self.class_mapping = config.get("class_mapping", {})
        
    @abstractmethod
    def load_annotations(self, source_path: str) -> List[ImageAnnotation]:
        """
        Load annotations from source format.
        
        Args:
            source_path: Path to source annotations
            
        Returns:
            List of image annotations
        """
        pass
    
    @abstractmethod
    def save_annotations(self, annotations: List[ImageAnnotation], 
                        output_path: str) -> None:
        """
        Save annotations in target format.
        
        Args:
            annotations: List of image annotations
            output_path: Output directory path
        """
        pass
    
    def convert(self, source_path: str, output_path: str, 
                split_ratios: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """
        Convert dataset from source to target format.
        
        Args:
            source_path: Path to source dataset
            output_path: Path to output dataset
            split_ratios: Train/val/test split ratios
            
        Returns:
            Conversion statistics
        """
        logger.info(f"Converting dataset from {self.source_format} to {self.target_format}")
        logger.info(f"Source: {source_path}")
        logger.info(f"Output: {output_path}")
        
        # Load annotations
        annotations = self.load_annotations(source_path)
        logger.info(f"Loaded {len(annotations)} annotations")
        
        # Split dataset if ratios provided
        if split_ratios:
            train_annotations, val_annotations, test_annotations = self._split_dataset(
                annotations, split_ratios
            )
            splits = {
                "train": train_annotations,
                "val": val_annotations,
                "test": test_annotations
            }
        else:
            splits = {"all": annotations}
        
        # Create output directory structure
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save annotations for each split
        stats = {}
        for split_name, split_annotations in splits.items():
            if split_annotations:
                split_output = output_dir / split_name
                self.save_annotations(split_annotations, str(split_output))
                stats[split_name] = len(split_annotations)
                logger.info(f"Saved {len(split_annotations)} {split_name} annotations")
        
        # Save dataset configuration
        self._save_dataset_config(output_dir, stats)
        
        logger.info("Dataset conversion completed successfully")
        return stats
    
    def _split_dataset(self, annotations: List[ImageAnnotation], 
                      split_ratios: Dict[str, float]) -> Tuple[List[ImageAnnotation], ...]:
        """
        Split dataset into train/val/test sets.
        
        Args:
            annotations: List of annotations
            split_ratios: Split ratios dictionary
            
        Returns:
            Tuple of (train, val, test) annotation lists
        """
        import random
        
        # Shuffle annotations
        random.seed(self.config.get("random_seed", 42))
        shuffled = annotations.copy()
        random.shuffle(shuffled)
        
        total = len(annotations)
        train_ratio = split_ratios.get("train", 0.7)
        val_ratio = split_ratios.get("val", 0.2)
        test_ratio = split_ratios.get("test", 0.1)
        
        # Normalize ratios
        total_ratio = train_ratio + val_ratio + test_ratio
        train_ratio /= total_ratio
        val_ratio /= total_ratio
        test_ratio /= total_ratio
        
        # Calculate split indices
        train_end = int(total * train_ratio)
        val_end = train_end + int(total * val_ratio)
        
        train_annotations = shuffled[:train_end]
        val_annotations = shuffled[train_end:val_end]
        test_annotations = shuffled[val_end:]
        
        return train_annotations, val_annotations, test_annotations
    
    def _save_dataset_config(self, output_dir: Path, stats: Dict[str, int]) -> None:
        """
        Save dataset configuration file.
        
        Args:
            output_dir: Output directory
            stats: Dataset statistics
        """
        config = {
            "path": str(output_dir),
            "train": "train/images" if "train" in stats else None,
            "val": "val/images" if "val" in stats else None,
            "test": "test/images" if "test" in stats else None,
            "nc": len(self.class_names),
            "names": {i: name for i, name in enumerate(self.class_names)},
            "stats": stats,
            "source_format": self.source_format,
            "target_format": self.target_format,
            "conversion_config": self.config
        }
        
        config_path = output_dir / "dataset.yaml"
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        logger.info(f"Saved dataset config to: {config_path}")
    
    def validate_annotations(self, annotations: List[ImageAnnotation]) -> Dict[str, Any]:
        """
        Validate annotations for consistency and correctness.
        
        Args:
            annotations: List of annotations to validate
            
        Returns:
            Validation report
        """
        report = {
            "total_images": len(annotations),
            "total_bboxes": 0,
            "class_distribution": {},
            "issues": []
        }
        
        for annotation in annotations:
            # Check if image file exists
            if not Path(annotation.image_path).exists():
                report["issues"].append(f"Image not found: {annotation.image_path}")
            
            # Count bboxes and classes
            report["total_bboxes"] += len(annotation.bboxes)
            
            for bbox in annotation.bboxes:
                class_id = bbox.class_id
                if class_id not in report["class_distribution"]:
                    report["class_distribution"][class_id] = 0
                report["class_distribution"][class_id] += 1
                
                # Validate bbox coordinates
                if not (0 <= bbox.x <= 1 and 0 <= bbox.y <= 1 and 
                       0 <= bbox.width <= 1 and 0 <= bbox.height <= 1):
                    report["issues"].append(
                        f"Invalid bbox coordinates in {annotation.image_path}: "
                        f"({bbox.x}, {bbox.y}, {bbox.width}, {bbox.height})"
                    )
        
        return report
    
    def get_conversion_summary(self) -> Dict[str, Any]:
        """
        Get summary of conversion configuration.
        
        Returns:
            Conversion summary
        """
        return {
            "converter_type": self.__class__.__name__,
            "source_format": self.source_format,
            "target_format": self.target_format,
            "class_names": self.class_names,
            "class_mapping": self.class_mapping,
            "config": self.config
        }
