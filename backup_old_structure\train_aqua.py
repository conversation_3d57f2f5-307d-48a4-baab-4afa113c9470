#!/usr/bin/env python3
"""
YOLOv13-N训练Aqua卫星数据集脚本

这个脚本使用YOLOv13-N模型训练Aqua卫星数据集。
数据集包含观测卫星的图像和边界框标注。

作者：AI助手
日期：2025-01-16
"""

import os
import sys
from pathlib import Path
import yaml
from ultralytics import YOLO
import torch
import gc

def check_dataset(dataset_path):
    """
    检查数据集是否存在且格式正确
    
    Args:
        dataset_path (str): 数据集路径
        
    Returns:
        bool: 数据集是否有效
    """
    dataset_path = Path(dataset_path)
    
    # 检查数据集目录是否存在
    if not dataset_path.exists():
        print(f"❌ 错误：数据集目录不存在：{dataset_path}")
        return False
    
    # 检查YAML配置文件
    yaml_file = dataset_path / "aqua_dataset.yaml"
    if not yaml_file.exists():
        print(f"❌ 错误：找不到数据集配置文件：{yaml_file}")
        return False
    
    # 检查必要的目录结构
    required_dirs = [
        "images/train",
        "images/val", 
        "images/test",
        "labels/train",
        "labels/val",
        "labels/test"
    ]
    
    for dir_name in required_dirs:
        dir_path = dataset_path / dir_name
        if not dir_path.exists():
            print(f"❌ 错误：缺少目录：{dir_path}")
            return False
        
        # 检查是否有文件
        files = list(dir_path.glob("*"))
        if not files:
            print(f"⚠️  警告：目录为空：{dir_path}")
    
    print("✅ 数据集检查通过")
    return True

def get_device():
    """获取可用的训练设备"""
    if torch.cuda.is_available():
        device = "0"  # 使用第一个GPU
        print(f"✅ 使用GPU训练：{torch.cuda.get_device_name(0)}")
        print(f"   GPU内存：{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    else:
        device = "cpu"
        print("⚠️  使用CPU训练（建议使用GPU以获得更好性能）")
    return device

def setup_training_environment():
    """设置训练环境"""
    # 设置环境变量以优化性能
    os.environ['CUDA_LAUNCH_BLOCKING'] = '0'
    os.environ['TORCH_CUDNN_V8_API_ENABLED'] = '1'
    
    # 清理GPU内存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def train_yolov13(
    dataset_path="Datasets/Aqua_YOLO",
    model_size="n",  # n=Nano, s=Small, l=Large, x=Extra Large
    epochs=100,
    batch_size=16,
    image_size=640,
    learning_rate=0.01,
    experiment_name="aqua_yolov13n"
):
    """
    训练YOLOv13模型
    
    Args:
        dataset_path (str): 数据集路径
        model_size (str): 模型大小 (n/s/l/x)
        epochs (int): 训练轮数
        batch_size (int): 批次大小
        image_size (int): 输入图像尺寸
        learning_rate (float): 学习率
        experiment_name (str): 实验名称
    """
    
    print("=" * 80)
    print("🚀 YOLOv13-N Aqua卫星数据集训练")
    print("=" * 80)
    
    # 检查数据集
    if not check_dataset(dataset_path):
        return False
    
    # 设置训练环境
    setup_training_environment()
    
    # 获取设备信息
    device = get_device()
    
    # 构建模型配置文件路径
    model_config = f"ultralytics/cfg/models/v13/yolov13.yaml"
    if not Path(model_config).exists():
        print(f"❌ 错误：找不到模型配置文件：{model_config}")
        return False
    
    print(f"📊 训练配置：")
    print(f"   模型大小：YOLOv13-{model_size.upper()}")
    print(f"   数据集：{dataset_path}")
    print(f"   训练轮数：{epochs}")
    print(f"   批次大小：{batch_size}")
    print(f"   图像尺寸：{image_size}")
    print(f"   学习率：{learning_rate}")
    print(f"   实验名称：{experiment_name}")
    print(f"   设备：{device}")
    print("-" * 80)
    
    try:
        # 创建YOLO模型
        print("📦 加载YOLOv13模型...")
        model = YOLO(model_config)
        
        # 设置模型规模
        model.model.yaml['scales'] = {
            'n': [0.50, 0.25, 1024],   # Nano
            's': [0.50, 0.50, 1024],   # Small  
            'l': [1.00, 1.00, 512],    # Large
            'x': [1.00, 1.50, 512]     # Extra Large
        }
        
        # 开始训练
        print("🏋️  开始训练...")
        results = model.train(
            data=f"{dataset_path}/aqua_dataset.yaml",
            epochs=epochs,
            batch=batch_size,
            imgsz=image_size,
            device=device,
            project="runs/train",
            name=experiment_name,
            
            # 学习率设置
            lr0=learning_rate,
            lrf=0.01,
            
            # 数据增强设置（针对卫星图像优化）
            hsv_h=0.015,  # 色调增强
            hsv_s=0.7,    # 饱和度增强
            hsv_v=0.4,    # 亮度增强
            degrees=0.0,  # 旋转角度（卫星通常不旋转）
            translate=0.1, # 平移
            scale=0.5,    # 缩放
            shear=0.0,    # 剪切
            perspective=0.0, # 透视变换
            flipud=0.0,   # 上下翻转
            fliplr=0.5,   # 左右翻转
            
            # Mosaic和Mixup设置
            mosaic=1.0,
            mixup=0.0,
            copy_paste=0.1,
            
            # 优化器设置
            optimizer='AdamW',
            momentum=0.937,
            weight_decay=0.0005,
            warmup_epochs=3.0,
            warmup_momentum=0.8,
            
            # 验证设置
            val=True,
            save=True,
            save_period=10,  # 每10轮保存一次检查点
            
            # 其他设置
            verbose=True,
            seed=42,
            deterministic=True,
            amp=True,  # 自动混合精度
            cache=False,  # 不缓存图像（节省内存）
            workers=4,
            exist_ok=True
        )
        
        print("\n" + "=" * 80)
        print("🎉 训练完成！")
        print("=" * 80)
        
        # 显示训练结果
        print("📈 训练结果摘要：")
        if hasattr(results, 'results_dict'):
            metrics = results.results_dict
            map50_95 = metrics.get('metrics/mAP50-95(B)', 'N/A')
            map50 = metrics.get('metrics/mAP50(B)', 'N/A')
            box_loss = metrics.get('train/box_loss', 'N/A')
            
            print(f"   最佳mAP50-95：{map50_95:.4f}" if isinstance(map50_95, (int, float)) else f"   最佳mAP50-95：{map50_95}")
            print(f"   最佳mAP50：{map50:.4f}" if isinstance(map50, (int, float)) else f"   最佳mAP50：{map50}")
            print(f"   最终损失：{box_loss:.4f}" if isinstance(box_loss, (int, float)) else f"   最终损失：{box_loss}")
        
        # 显示保存位置
        save_dir = Path("runs/train") / experiment_name
        print(f"\n📁 模型保存位置：")
        print(f"   最佳模型：{save_dir}/weights/best.pt")
        print(f"   最后模型：{save_dir}/weights/last.pt")
        print(f"   训练日志：{save_dir}")
        
        # 运行验证
        print("\n🔍 在测试集上验证模型...")
        model.val(
            data=f"{dataset_path}/aqua_dataset.yaml",
            split='test',
            save_json=True,
            save_hybrid=True
        )
        
        return True
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误：{e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🛰️  YOLOv13-N Aqua卫星数据集训练脚本")
    print("作者：AI助手")
    print("=" * 80)
    
    # 检查当前工作目录
    current_dir = Path.cwd()
    print(f"当前工作目录：{current_dir}")
    
    # 默认参数
    dataset_path = "Datasets/Aqua_YOLO"
    
    # 检查数据集是否存在
    if not Path(dataset_path).exists():
        print(f"❌ 数据集目录不存在：{dataset_path}")
        print("请先运行数据集转换脚本：python convert_aqua_to_yolo.py")
        return
    
    # 训练参数设置
    training_config = {
        "dataset_path": dataset_path,
        "model_size": "n",  # 使用Nano版本
        "epochs": 100,      # 训练轮数
        "batch_size": 16,   # 批次大小
        "image_size": 640,  # 图像尺寸
        "learning_rate": 0.01,  # 学习率
        "experiment_name": "aqua_yolov13n_v1"
    }
    
    print(f"\n开始训练，配置参数：")
    for key, value in training_config.items():
        print(f"  {key}: {value}")
    
    # 开始训练
    success = train_yolov13(**training_config)
    
    if success:
        print("\n🎊 训练成功完成！")
        print("\n下一步建议：")
        print("1. 查看训练日志和可视化结果")
        print("2. 使用最佳模型进行预测测试")
        print("3. 如果效果不理想，可以调整超参数重新训练")
        print("\n使用训练好的模型进行预测：")
        print("python -c \"from ultralytics import YOLO; model = YOLO('runs/train/aqua_yolov13n_v1/weights/best.pt'); model.predict('path/to/image.jpg', save=True)\"")
    else:
        print("\n❌ 训练失败，请检查错误信息并重试")

if __name__ == "__main__":
    main()