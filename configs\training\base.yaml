# Base Training Configuration
# Standard training settings for most experiments

# Training parameters
epochs: 100
batch_size: 16
imgsz: 640

# Optimizer settings
optimizer:
  name: "AdamW"
  lr0: 0.001      # Initial learning rate
  lrf: 0.01       # Final learning rate factor
  momentum: 0.937
  weight_decay: 0.0005
  warmup_epochs: 3
  warmup_momentum: 0.8
  warmup_bias_lr: 0.1

# Learning rate scheduler
scheduler:
  name: "cosine"  # linear, cosine
  patience: 50    # Early stopping patience

# Loss function weights
loss:
  box: 7.5        # Box loss weight
  cls: 0.5        # Classification loss weight
  dfl: 1.5        # Distribution focal loss weight

# Training settings
training:
  amp: true       # Automatic Mixed Precision
  cache: false    # Cache images for faster training
  device: null    # Device (auto-detected if null)
  workers: 8      # Number of dataloader workers
  project: "runs/train"
  name: null      # Experiment name (auto-generated if null)
  exist_ok: false # Overwrite existing experiment
  pretrained: true
  verbose: true
  seed: 0
  deterministic: true
  single_cls: false
  rect: false     # Rectangular training
  cos_lr: false   # Cosine learning rate scheduler
  close_mosaic: 10 # Disable mosaic in last N epochs
  resume: false   # Resume training
  fraction: 1.0   # Dataset fraction to use

# Validation settings
validation:
  val: true       # Validate during training
  split: "val"    # Validation split
  save_period: -1 # Save checkpoint every N epochs (-1 to disable)
  save_best: true # Save best checkpoint
  save_last: true # Save last checkpoint

# Data augmentation (can be overridden by dataset config)
augmentation:
  hsv_h: 0.015
  hsv_s: 0.7
  hsv_v: 0.4
  degrees: 0.0
  translate: 0.1
  scale: 0.5
  shear: 0.0
  perspective: 0.0
  flipud: 0.0
  fliplr: 0.5
  mosaic: 1.0
  mixup: 0.0
  copy_paste: 0.0

# Callbacks
callbacks:
  - name: "ModelCheckpoint"
    save_best: true
    save_last: true
  - name: "EarlyStopping"
    patience: 50
  - name: "LearningRateMonitor"
    logging_interval: "epoch"
