#!/usr/bin/env python3
"""
Training Script for YOLO Baseline Platform

This script provides a command-line interface for training YOLO models
with Hydra configuration management.

Usage:
    python scripts/train.py model=yolov13n dataset=aqua training=base
    python scripts/train.py model=yolov11s dataset=aqua training.epochs=200
    python scripts/train.py --config-path=configs --config-name=config model=yolov13n
"""

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import hydra
from omegaconf import DictConfig, OmegaConf
import logging
from typing import Optional

from training.trainer import YOLOTrainer
from utils.logging import setup_logging, TrainingLogger, log_system_info
from utils.config import resolve_config_path, load_config
from models.registry import list_available_models

logger = logging.getLogger(__name__)


@hydra.main(version_base=None, config_path="../configs", config_name="config")
def train_model(cfg: DictConfig) -> Optional[float]:
    """
    Main training function with Hydra configuration.
    
    Args:
        cfg: Hydra configuration
        
    Returns:
        Best validation mAP if available
    """
    try:
        # Setup logging
        setup_logging(OmegaConf.to_container(cfg.get("logging", {})))
        
        # Log system information
        log_system_info()
        
        # Log configuration
        logger.info("Training Configuration:")
        logger.info(OmegaConf.to_yaml(cfg))
        
        # Validate model availability
        model_name = cfg.model.architecture.name
        available_models = list_available_models()
        
        if model_name not in available_models:
            logger.error(f"Model '{model_name}' not available. Available models: {available_models}")
            return None
        
        # Create experiment name if not provided
        if not cfg.training.get("name"):
            experiment_name = f"{model_name}_{cfg.dataset.name}_{cfg.training.get('epochs', 100)}ep"
            cfg.training.name = experiment_name
        
        # Setup experiment logger
        exp_logger = TrainingLogger(cfg.training.name)
        exp_logger.log_training_start(OmegaConf.to_container(cfg))
        
        # Create trainer
        trainer = YOLOTrainer(OmegaConf.to_container(cfg))
        
        # Extract configurations
        model_config = OmegaConf.to_container(cfg.model)
        dataset_config = OmegaConf.to_container(cfg.dataset)
        training_config = OmegaConf.to_container(cfg.training)
        
        # Validate dataset path
        dataset_path = Path(cfg.paths.data_root) / dataset_config["path"]
        if not dataset_path.exists():
            logger.error(f"Dataset path not found: {dataset_path}")
            logger.info("Please ensure the dataset is converted and available.")
            return None
        
        # Update dataset path to absolute path
        dataset_config["path"] = str(dataset_path / "dataset.yaml")
        
        # Train model
        logger.info("Starting model training...")
        results = trainer.train(model_config, dataset_config, training_config)
        
        # Log training completion
        exp_logger.log_training_end(results)
        
        # Save results
        results_dir = Path(cfg.paths.experiments_root) / "training_results"
        results_file = results_dir / f"{cfg.training.name}_results.json"
        trainer.save_results(results, results_file)
        
        # Extract best mAP for Hydra optimization
        best_map = None
        if results and "results" in results:
            training_results = results["results"]
            if hasattr(training_results, "results_dict"):
                metrics = training_results.results_dict
                best_map = metrics.get("metrics/mAP50-95(B)", metrics.get("val/mAP50-95"))
        
        logger.info(f"Training completed successfully. Best mAP: {best_map}")
        return best_map
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        if 'exp_logger' in locals():
            exp_logger.log_error(e)
        raise


def main():
    """Main entry point for standalone execution."""
    try:
        train_model()
    except Exception as e:
        logger.error(f"Training script failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
